# AL Profiler Guide for Dynamics 365 Business Central

## Overview

The AL Profiler is a powerful performance analysis tool introduced in Dynamics 365 Business Central 2021 Release Wave 2, with sampling profiling added in 2022 Release Wave 1. It provides developers with the ability to profile AL code execution, identify performance bottlenecks, and optimize their applications through both instrumentation and sampling profiling methods.

## Key Features

- **Dual Profiling Methods**: Instrumentation profiling (precise) and sampling profiling (faster)
- **Performance Analysis**: Analyze execution time of AL code procedures and events
- **Integrated Debugging**: Works seamlessly with Visual Studio Code snapshot debugging
- **Call Stack Analysis**: Provides both top-down and bottom-up call stack views
- **Visual Graph Display**: Interactive graph visualization of method calls with color-coded layers
- **Profiler CodeLens**: Inline performance metrics displayed directly in source code
- **Filtering and Navigation**: Advanced filtering capabilities and keyboard shortcuts
- **In-Client Profiling**: Built-in Performance Profiler page in Business Central

## Prerequisites

- Dynamics 365 Business Central 2021 Release Wave 2 or later
- Visual Studio Code with AL Language extension
- Properly configured launch.json for snapshot debugging
- Administrative permissions (avoid "Run as administrator" mode for graph display)

## Configuration

### 1. Instrumentation Profiling Setup

For precise profiling with complete method call tracing, configure your `launch.json` file:

```json
{
    "name": "Instrumentation Profile",
    "type": "al",
    "userId": "555",
    "request": "snapshotInitialize",
    "environmentType": "OnPrem",
    "server": "http://localserver",
    "serverInstance": "BC200",
    "authentication": "Windows",
    "breakOnNext": "WebClient",
    "executionContext": "DebugAndProfile",
    "profilingType": "Instrumentation"
}
```

### 2. Sampling Profiling Setup

For faster profiling with trend analysis, configure for sampling:

```json
{
    "name": "Sampling Profile",
    "type": "al",
    "userId": "555",
    "request": "snapshotInitialize",
    "environmentType": "OnPrem",
    "server": "http://localserver",
    "serverInstance": "BC200",
    "authentication": "Windows",
    "breakOnNext": "WebClient",
    "executionContext": "Profile",
    "profilingType": "Sampling",
    "profileSamplingInterval": 100
}
```

### 3. Execution Context Options

The `executionContext` parameter supports three modes:

| Option | Description | Use Case |
|--------|-------------|----------|
| **"Debug"** | No profile information collected | Pure debugging sessions |
| **"DebugAndProfile"** | Both debugging and profiling available | Full analysis with breakpoints |
| **"Profile"** | Only profiling, snappoints ignored | Performance-focused analysis |

### 4. Profiling Type Options

| Type | Accuracy | Performance Impact | Duration Limits |
|------|----------|-------------------|-----------------|
| **Instrumentation** | High - captures all method calls | Higher load on NST | No specific limit |
| **Sampling** | Lower - samples at intervals | Lower load on NST | Max 10 minutes, 2000 stack frames |

## How to Use the AL Profiler

### Step 1: Initialize Profiling Session

1. Press `Ctrl+Shift+P` to open the Command Palette
2. Select "AL: Initialize Snapshot Debugging" or press `F7`
3. Choose your configured profiling launch configuration

### Step 2: Execute Code to Profile

1. Navigate to the Business Central interface
2. Perform the actions you want to analyze (e.g., posting documents, running reports)
3. Complete all operations you want to include in the profile

### Step 3: End Profiling Session

1. Press `Alt+F7` to end the profiling session
2. The snapshot will be automatically downloaded to your local machine
3. Save the snapshot file for analysis

### Step 4: Generate Profile File

Generate the profile file using one of two methods:

**Method 1 - Command Palette:**
1. Press `Ctrl+Shift+P`
2. Select "AL: Generate profile file"
3. Choose the downloaded snapshot from the dropdown

**Method 2 - File Explorer:**
1. In VS Code Explorer, right-click the snapshot file
2. Select "Generate Profile File"
3. A `.alcpuprofile` file will be generated with detailed execution data

## Profile Analysis

### Visual Graph Interface

The profile opens in Visual Studio Code with an interactive graph visualization:

#### Color-Coded Layer System
| Layer | Color | Description |
|-------|-------|-------------|
| System Application | Green | Core system functionality |
| Base Application | Magenta | Standard BC application |
| Other Extensions | Yellow | Third-party extensions |
| System | Blue | System-level operations |
| Custom | Named extension | Your custom code |

**Note**: Colors can be customized using the `al.profilerColors` configuration setting.

#### View Modes

**Top-Down View:**
- Shows call sequence from parent to child methods
- Child nodes represent methods called FROM the parent
- Includes **Hit Count** column showing method call frequency
- Best for understanding execution flow

**Bottom-Up View:**
- Shows reverse call stack
- Child nodes represent methods that CALLED the parent
- Sortable **Self-time** and **Total time** columns
- Best for identifying performance bottlenecks

#### Key Performance Metrics

- **Self-time**: Time spent in method only (excluding sub-calls)
- **Total time**: Self-time PLUS time spent in called methods
- **Hit Count**: Number of times method was called (top-down only)

### Navigation and Filtering

#### Keyboard Shortcuts
| Shortcut | Action |
|----------|--------|
| `Enter` | Toggle expand/collapse node |
| `Left Arrow` | Collapse node |
| `Right Arrow` | Expand node |
| `Tab+Enter` | Navigate to source code |
| `F12` | Jump to selected node |
| `Home/End` | Jump to first/last node |
| `Up/Down Arrow` | Navigate between nodes |
| `-` (minus) | Collapse all nodes |
| `*` (star) | Expand one level for all nodes |

#### Advanced Filtering

Filter syntax: `@column <operator> <value>`

**Column Aliases:**
- `f` = function, `u` = url, `p` = path
- `s` = selfTime, `t` = totalTime, `h` = hitCount
- `id` = id, `ot` = objectType, `on` = objectName
- `da` = declaringApplication

**Filter Examples:**
```
@t > 1000        # Total time greater than 1 second
@h > 20          # Hit count larger than 20  
@da ~= Ba*       # Applications starting with "Ba"
@s > 500         # Self-time greater than 500ms
```

### Profiler CodeLens Integration

#### Setup Requirements
1. Enable general CodeLens: `"editor.codeLens": true`
2. Enable AL Profiler CodeLens: `"al.areProfileLensesSupported": true` (default: true)
3. Set minimum threshold: `"al.statementLensMin": 500` (default: 500ms)

#### Features
- **Inline Metrics**: Time spent and hit count displayed in source code
- **Hover Details**: Detailed timing information on mouse hover
- **Automatic Updates**: Updates when opening profiler files
- **Threshold Filtering**: Only shows statements above configured time limit

## Best Practices

### When to Use AL Profiler

- **User-Reported Slowness**: When users report specific pages or processes are slower than expected
- **Pre-AppSource Publishing**: Optimize extensions before publishing to AppSource marketplace
- **Performance Validation**: Validate improvements after code changes
- **Complex Process Analysis**: Identify which parts of complex processes consume the most time
- **Extension Development**: Ensure custom extensions don't negatively impact system performance

### Choosing Between Profiling Types

**Use Instrumentation Profiling When:**
- Precise timing data is required
- Detailed method call analysis needed
- Debugging along with profiling
- Working with shorter processes
- Need complete call stack information

**Use Sampling Profiling When:**
- Quick performance trend analysis needed
- Working with longer running processes (up to 10 minutes)
- Minimal performance impact required
- Initial performance assessment
- In-client profiling via Performance Profiler page

### Profiling Strategies

1. **Isolate Scenarios**: Profile specific business processes in isolation
2. **Consistent Environment**: Use the same test data and environment conditions
3. **Multiple Runs**: Take several profiles to account for variations
4. **Focus Areas**: Target known problem areas or newly developed code

### Optimization Guidelines

1. **Database Queries**: Look for inefficient table scans or missing filters
2. **Loop Optimization**: Identify expensive operations inside loops
3. **Unnecessary Calculations**: Find redundant or repeated computations
4. **Event Handler Performance**: Check custom event subscribers for efficiency

## Common Performance Patterns

### Database Access Optimization

- Use appropriate filters before record iteration
- Minimize the number of database calls
- Optimize SETRANGE and SETFILTER usage
- Consider using temporary tables for complex data manipulation

### Code Structure Improvements

- Avoid deep nesting in loops
- Cache frequently accessed values
- Use efficient data structures
- Minimize string concatenation in loops

## In-Client Performance Profiler

### Business Central Built-in Profiler

Access the Performance Profiler directly in Business Central:

1. **Navigate** to the **Performance Profiler** page in Business Central
2. **Start Recording** to begin sampling profiling session
3. **Perform Actions** you want to analyze
4. **Stop Recording** to generate the profile
5. **Download** the `.alcpuprofile` file for analysis

### Sharing and Collaboration

- **Share Button**: Generate OneDrive link for easy sharing with colleagues
- **Download Button**: Save profile files locally
- **Upload Button**: Import existing profile files for analysis
- **Clear Button**: Remove profile data to start fresh recordings

### Scheduled Performance Profiling

- **Advanced Scheduling**: Set up automated profiling sessions
- **Sampling Frequency Control**: Adjust sampling intervals (default varies)
- **Activity Duration Threshold**: Filter out short-lived activities
- **Maximum Retention**: One week retention for schedules and data
- **Debugging Restriction**: Cannot attach debugger during scheduled profiling

## Troubleshooting

### Common Issues

**Graph Not Displaying:**
- **Issue**: Visual Studio Code running as administrator prevents graph display
- **Solution**: Launch VS Code from command line with `--no-sandbox` flag, or run without admin privileges

**Profile File Not Generated:**
- Ensure Business Central 2021 Release Wave 2 or later
- Verify snapshot properly downloaded and saved
- Check AL Language extension is up to date
- Confirm `profilingType` parameter is correctly set

**Empty or Incomplete Profiles:**
- Verify `executionContext` is set to "Profile" or "DebugAndProfile"
- Ensure profiling session captured the intended operations
- Check that the profiled code actually executed during the session
- For sampling: verify process duration was sufficient for sampling interval

**Sampling Profiling Limitations:**
- **Duration Limit**: Maximum 10-minute sessions
- **Stack Frame Limit**: Limited to 2000 stack frame entries  
- **Accuracy**: May miss calls shorter than sampling interval
- **Timing Approximation**: Durations are approximate based on sampling frequency

**CodeLens Not Showing:**
- Verify `"editor.codeLens": true` in VS Code settings
- Check `"al.areProfileLensesSupported": true` setting
- Ensure statement execution time exceeds `al.statementLensMin` threshold (default: 500ms)

## Integration with Development Workflow

### Code Review Process

1. Profile new features during development
2. Set performance benchmarks for critical operations
3. Include profile analysis in code review checklist
4. Document performance characteristics of major functions

### Continuous Performance Monitoring

1. Establish baseline profiles for core business processes
2. Regular profiling of critical system operations
3. Monitor performance impact of updates and changes
4. Maintain performance regression test suite

## Advanced Usage

### Custom Event Profiling

Profile custom events and publishers/subscribers to ensure efficient implementation:

```al
// Example: Profile custom event handling
[EventSubscriber(ObjectType::Page, Page::"Sales Order", 'OnAfterValidateEvent', 'No.', false, false)]
procedure OnSalesOrderNoValidate(var Rec: Record "Sales Header")
begin
    // Ensure this subscriber doesn't impact performance
    // Profile to verify efficiency
end;
```

### Extension Performance Analysis

When developing extensions:

1. Profile extension initialization
2. Analyze impact on standard BC processes
3. Verify event subscriber performance
4. Test with realistic data volumes

## Related Tools and Integration

### SQL Behavior Debugging
- **Database Statistics**: View SQL execution insights during debugging
- **SQL Statement Analysis**: Examine actual SQL queries generated by AL code
- **Performance Metrics**: Track SQL rows read, execution count, and latency
- **Configuration**: Enable via `enableSQLInformationDebugger` in launch.json

### Telemetry Integration
- **Azure Application Insights**: Long-term performance monitoring
- **Power BI Analytics**: Visual analysis of performance trends
- **KQL Queries**: Custom analysis of telemetry data
- **Pattern Analysis**: Identify performance issues across sessions

### Web Service Profiling
- **OData/SOAP Endpoints**: Profile web service calls
- **API Pages/Queries**: Analyze API performance
- **Configuration**: Set `breakOnNext` to "WebServiceClient"

## Maxwell Customizations Integration

### Project-Specific Profiling Strategies

For the Maxwell Customizations PTE, focus profiling on:

1. **Package Management Operations**:
   - Profile `MaxwellBasicFunctionsMXW` utility functions
   - Analyze package creation workflows in `MaxwellPurchaseMngtMXW`
   - Monitor barcode scanning performance in `MaxwellSalesMngtMXW`

2. **Quality Control Integration**:
   - Profile QC document processing workflows
   - Analyze bulk "Accept All Quality Control Documents" operations
   - Monitor QC status validation performance

3. **Warehouse Operations**:
   - Profile warehouse receipt processing
   - Analyze package transfer operations
   - Monitor production output journal creation

### Recommended Profile Points

```al
// Example: Profile package creation workflow
procedure CreatePackagesFromReceipt(var PackageCreation: Record "Package Creation MXW")
begin
    // Set breakpoint here for profiling
    BasicFunctions.ValidateQualityControlStatus(PackageCreation);
    // Profile the heavy lifting operations
    CreateMultiplePackages(PackageCreation);
end;
```

## Conclusion

The AL Profiler is an essential tool for Dynamics 365 Business Central developers focused on creating high-performance applications. With both instrumentation and sampling profiling methods, visual graph analysis, and integrated CodeLens support, it provides comprehensive performance insights.

**Key Benefits:**
- **Dual Profiling Methods**: Choose between precision (instrumentation) and speed (sampling)
- **Visual Analysis**: Interactive graph visualization with filtering and navigation
- **Development Integration**: CodeLens integration and source navigation
- **In-Client Options**: Built-in Performance Profiler for business users
- **Collaboration Support**: Easy sharing and uploading of profile data

**For Maxwell Customizations Development:**
- Profile package management workflows regularly
- Focus on Quality Control integration performance
- Monitor warehouse operation efficiency
- Use sampling profiling for initial analysis, instrumentation for detailed optimization

Regular use of the AL Profiler leads to more efficient code, better user experiences, and more maintainable applications in the Dynamics 365 Business Central ecosystem, ensuring optimal performance for package-centric warehouse management scenarios.