# Warehouse Shipment Line Detail Creation Plan

## Task Overview
Create warehouse shipment line detail table and page similar to warehouse receipt line detail structure.

## Analysis of Existing Warehouse Receipt Line Detail Structure
- Table: `Warehouse Receipt Line Dtl MXW` (ID: 60002)  
- Page: `Whse. Receipt Line Details MXW` (ID: 60002)

## Key Fields in Receipt Line Detail:
- Document No., Document Line No., Line No. (Primary Key)
- Package No., Item No., Item Description, Quantity, Lot No.
- Expiration Date, Item Tracking Info Assigned, Received
- Variant Code, Unit of Measure Code, Purchase Order No.
- Package Count, Total Package Qty.

## Objects to Create:
1. **Table: Warehouse Shipment Line Dtl MXW** (ID: 60003)
   - Similar field structure but adapted for shipment context
   - Replace "Received" field with "Shipped" 
   - Replace "Purchase Order No." with "Sales Order No."
   - Similar validation logic and triggers

2. **Page: Whse. Shipment Line Details MXW** (ID: 60003)
   - List page with similar layout
   - Actions for printing shipment labels
   - Similar field structure adapted for shipment

## Naming Conventions
- Follow existing MXW naming pattern
- Use "Shipment" instead of "Receipt" 
- Maintain same field naming where applicable

## Next Available IDs
- Table ID: 60003
- Page ID: 60003