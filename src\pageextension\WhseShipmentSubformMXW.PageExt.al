pageextension 60013 "Whse. Shipment Subform MXW" extends "Whse. Shipment Subform"
{
    layout
    {
        addafter(Description)
        {
            field("Package Count MXW"; Rec."Package Count MXW")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the number of packages for this warehouse shipment line.';
            }
            field("Total Package Quantity MXW"; Rec."Total Package Quantity MXW")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the total quantity from all packages for this warehouse shipment line.';
            }
            field("Item Tracking Info Assignd MXW"; Rec."Item Tracking Info Assignd MXW")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies whether item tracking information is assigned.';
            }
        }
    }

    actions
    {
        addafter("&Line")
        {
            group("Maxwell MXW")
            {
                Caption = 'Maxwell';
                action("Assign Item Tracking Info. MXW")
                {
                    ApplicationArea = All;
                    Caption = 'Assign Item Tracking Info.';
                    Image = LotInfo;
                    ToolTip = 'Assigns item tracking information to the warehouse shipment line.';

                    trigger OnAction()
                    begin
                        MaxwellSalesMngt.AssignItemTrackingInformationFromWarehouseShipmentLine(Rec);
                    end;
                }
                action("Warehouse Shipment Line Details MXW")
                {
                    ApplicationArea = All;
                    Caption = 'Line Details';
                    Image = ViewDetails;
                    ToolTip = 'View the warehouse shipment line details with package information.';
                    RunObject = page "Whse. Shipment Line Dtl MXW";
                    RunPageLink = "Document No." = field("No."), "Document Line No." = field("Line No.");
                }
            }
        }
    }

    var
        MaxwellSalesMngt: Codeunit "Maxwell Sales Mngt. MXW";
}
