page 60024 "Package Transfer Subpage MXW"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Subpage';
    PageType = ListPart;
    SourceTable = "Package Transfer Line MXW";
    InsertAllowed = false;
    SourceTableView = sorting("Document No.", "Line No.") order(descending);

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                    Editable = false;
                    QuickEntry = false;

                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        PackageNoInformation.SetRange("Package No.", Rec."Package No.");
                        if PackageNoInformation.FindFirst() then
                            Page.Run(Page::"Package No. Information Card", PackageNoInformation);
                    end;
                }

                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }

                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                    QuickEntry = false;
                }

                field(Description; Rec.Description)
                {
                    Editable = false;
                    QuickEntry = false;
                }

                field("Current Quantity"; Rec.Quantity)
                {
                    Caption = 'Current Quantity';
                    ToolTip = 'Specifies the value of the Current Quantity field.';
                    Editable = false;
                    QuickEntry = false;
                }

                field("Quantity To Transfer"; Rec."Quantity To Transfer")
                {
                    QuickEntry = false;
                }

                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }

                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(AssignMaxQuantity)
            {
                Caption = 'Assign Max Quantity';
                Image = ServiceAccessories;
                ToolTip = 'Assigns the maximum available quantity to transfer.';

                trigger OnAction()
                begin
                    Rec.Validate("Quantity To Transfer", Rec.Quantity);
                    Rec.Modify(true);
                end;
            }
        }
    }

    // trigger OnAfterGetRecord()
    // var
    //     PackageNoInformation: Record "Package No. Information";
    // begin
    //     PackageNoInformation.SetAutoCalcFields(Inventory);
    //     if PackageNoInformation.Get(Rec."Item No.", Rec."Variant Code", Rec."Package No.") then
    //         LiveQuantity := PackageNoInformation.Inventory
    //     else
    //         LiveQuantity := 0;
    // end;

    // var
    //     LiveQuantity: Decimal;
}
