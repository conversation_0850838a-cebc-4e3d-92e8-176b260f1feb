page 60023 "Package Transfer Orders MXW"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Orders';
    PageType = List;
    SourceTable = "Package Transfer Header MXW";
    UsageCategory = Documents;
    Editable = false;
    CardPageId = "Package Transfer Order MXW";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }

                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                }

                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                }

                field("Posting Date"; Rec."Posting Date")
                {
                }

                field(Shipped; Rec.Shipped)
                {
                }

                field(Received; Rec.Received)
                {
                }

                field("Total Transfer Quantity"; Rec."Total Transfer Quantity")
                {
                }

                field("Palette Count"; Rec."Palette Count")
                {
                }

                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}
