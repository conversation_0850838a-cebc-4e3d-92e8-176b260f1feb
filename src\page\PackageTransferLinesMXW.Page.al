page 60025 "Package Transfer Lines MXW"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Lines';
    PageType = List;
    SourceTable = "Package Transfer Line MXW";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }

                field("Line No."; Rec."Line No.")
                {
                }

                field("Package No."; Rec."Package No.")
                {
                }

                field("Item No."; Rec."Item No.")
                {
                }

                field("Variant Code"; Rec."Variant Code")
                {
                }

                field(Description; Rec.Description)
                {
                }

                field(Quantity; Rec.Quantity)
                {
                }

                field("Lot No."; Rec."Lot No.")
                {
                }

                field("Quantity To Transfer"; Rec."Quantity To Transfer")
                {
                }

                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                }

                field("Total Qty. on Location"; Rec."Total Qty. on Location")
                {
                }

                field("Total Qty. on Loc. Trans"; Rec."Total Qty. on Loc. Trans")
                {
                }

                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                }

                field(Received; Rec.Received)
                {
                }

                field("Posting Date"; Rec."Posting Date")
                {
                }

                field("Package Creation Date"; Rec."Package Creation Date")
                {
                }

                field("Current Package Location"; Rec."Current Package Location")
                {
                }

                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }

                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }

                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }

                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }

                field(SystemModifiedBy; Rec.SystemModifiedBy)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.';
                }
            }
        }
    }
}
