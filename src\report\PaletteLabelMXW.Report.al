report 60080 "Palette Label MXW"
{
    ApplicationArea = All;
    Caption = 'Palette Label';
    UsageCategory = ReportsAndAnalysis;
    DefaultLayout = RDLC;
    RDLCLayout = 'src/reportlayout/PaletteLabelMXW2.Label.rdlc';

    dataset
    {
        dataitem(PackageNoInformation; "Package No. Information")
        {
            column(PackageNo; "Package No.")
            {
            }
            column(LocationCodeMXW; "Location Code MXW")
            {
            }
            column(PackageNoBarCode; PackageNoBarCode)
            {
            }
            column(PackageNoQRCode; PackageNoQRCode)
            {
            }
            column(CreatedAtMXW_PackageNoInformation; SystemCreatedAt)
            {
            }
            column(DocumentNoMXW_PackageNoInformation; "Document No. MXW")
            {
            }
            column(VariantCode_PackageNoInformation; "Variant Code")
            {
            }
            column(SystemModifiedAt_PackageNoInformation; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy_PackageNoInformation; SystemModifiedBy)
            {
            }
            column(SystemId_PackageNoInformation; SystemId)
            {
            }
            column(SystemCreatedBy_PackageNoInformation; SystemCreatedBy)
            {
            }
            column(LotNoMXW_PackageNoInformation; "Lot No. MXW")
            {
            }
            column(ItemNo_PackageNoInformation; "Item No.")
            {
            }
            column(LabelQuantityMXW_PackageNoInformation; "Label Quantity MXW")
            {
            }
            column(Description_PackageNoInformation; Description)
            {
            }
            column(ExpirationDateMXW_PackageNoInformation; "Expiration Date MXW")
            {
            }
            column(CreatedByFullName_PackageNoInformation; CreatedByFullName)
            {
            }
            column(BaseUnitofMeasure_Item; Item."Base Unit of Measure")
            {
            }
            dataitem("Lot No. Information"; "Lot No. Information")
            {
                DataItemLink = "Lot No." = field("Lot No. MXW");

                column(ProductionOrderNoMXW_LotNoInformation; "Production Order No. MXW")
                {
                }
            }
            trigger OnAfterGetRecord()
            var
                BarcodeFontProvider: Interface "Barcode Font Provider";
                BarcodeFontProvider2D: Interface "Barcode Font Provider 2D";
                BarcodeString: Text;
            begin
                // Set user full names
                CreatedByFullName := BasicFunc.GetUserFullNameFromSecurityId(SystemCreatedBy);

                if Item.Get("Item No.") then;//

                BarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
                BarcodeFontProvider2D := Enum::"Barcode Font Provider 2D"::IDAutomation2D;

                if "Package No." <> '' then begin
                    BarcodeString := "Package No.";

                    BarcodeFontProvider.ValidateInput(BarcodeString, BarcodeSymbology);

                    PackageNoBarCode := BarcodeFontProvider.EncodeFont(BarcodeString, BarcodeSymbology);
                    PackageNoQRCode := BarcodeFontProvider2D.EncodeFont(BarcodeString, BarcodeSymbology2D);
                end
            end;
        }
    }

    trigger OnInitReport()
    begin
        BarcodeSymbology := Enum::"Barcode Symbology"::Code128;
        BarcodeSymbology2D := Enum::"Barcode Symbology 2D"::"QR-Code";
    end;

    var
        Item: Record Item;
        BasicFunc: Codeunit "Maxwell Basic Functions MXW";
        CreatedByFullName: Text[80];
        BarcodeSymbology: Enum "Barcode Symbology";
        BarcodeSymbology2D: Enum "Barcode Symbology 2D";
        PackageNoBarCode: Text;
        PackageNoQRCode: Text;
}
