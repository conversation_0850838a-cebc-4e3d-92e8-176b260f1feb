pageextension 60012 "Warehouse Shipment MXW" extends "Warehouse Shipment"
{
    layout
    {
        addafter(General)
        {
            group("Barcode MXW")
            {
                Caption = 'Barcode Reading';
                field("Barcode Text MXW"; Rec."Barcode Text MXW")
                {
                    ApplicationArea = All;
                    ShowCaption = false;

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Last Scanned Barcode MXW"; Rec."Last Scanned Barcode MXW")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
            }
        }
    }

    actions
    {
        addfirst("F&unctions")
        {
            action("AssignItemTrackingInfo MXW")
            {
                ApplicationArea = All;
                Caption = 'Assign Item Tracking Info.';
                Image = LotInfo;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Specifies that item tracking information is assigned to warehouse shipment lines.';

                trigger OnAction()
                begin
                    MaxwellSalesMngt.AssignItemTrackingInformationFromWarehouseShipmentHeader(Rec);
                end;
            }
        }
        addafter("&Print")
        {
            group("Maxwell MXW")
            {
                Caption = 'Maxwell';
                action("Warehouse Shipment Line Details MXW")
                {
                    ApplicationArea = All;
                    Caption = 'Warehouse Shipment Line Details';
                    Image = ViewDetails;
                    ToolTip = 'Specifies that you view the warehouse shipment line details with package information.';
                    RunObject = page "Whse. Shipment Line Dtl MXW";
                    RunPageLink = "Document No." = field("No.");
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;
                    PromotedOnly = true;
                }
            }
        }
    }

    var
        MaxwellSalesMngt: Codeunit "Maxwell Sales Mngt. MXW";
}
