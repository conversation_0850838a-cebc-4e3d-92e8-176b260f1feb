tableextension 60003 "Warehouse Request MXW" extends "Warehouse Request"
{
    fields
    {
        field(60000; "Available Qty.-to Receive MXW"; Decimal)
        {
            Caption = 'Available Quantity-to Receive';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Purchase Line"."Whse. Rcpt. Qty-to Receive MXW" where("Document No." = field("Source No.")));
            ToolTip = 'Specifies the available quantity to receive.';
        }
        field(60001; "Destination Name MXW"; Text[100])
        {
            Caption = 'Destination Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Vendor.Name where("No." = field("Destination No.")));
            ToolTip = 'Specifies the destination name.';
        }
    }
}
