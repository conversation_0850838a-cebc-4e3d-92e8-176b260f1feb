# Project Structure
- All source code is under `src/`
  - `codeunit/`: Business logic (e.g., MaxwellPurchaseMngtMXW.Codeunit.al)
  - `table/`, `tableextension/`: Custom tables and extensions
  - `page/`, `pageextension/`: UI and customizations
  - `report/`, `reportlayout/`: Custom reporting
  - `enum/`: Custom enums
  - `permissionset/`: Permission sets
- Analyzer and build config in root (custom.ruleset.json, app.json)
- .vscode/ for workspace config (launch.json, settings.json, mcp.json)
- .serena/ for Serena metadata and memories
- Translations/ for XLF files
- Follows standard AL extension layout for BC