table 60001 "Package Creation MXW"
{
    Caption = 'Package Creation';
    DataClassification = CustomerContent;
    TableType = Temporary;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the document number.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the line number within the document.';
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the item number.';
            trigger OnValidate()
            var
                Item: Record Item;
                BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
            begin
                if Item.Get(Rec."Item No.") then
                    Rec."Item Description" := BasicFuncs.GetItemDescription(Rec."Item No.", '');
            end;
        }
        field(4; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies a description of the item.';
        }
        field(5; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the lot number.';
        }
        field(6; "Package Count"; Integer)
        {
            Caption = 'Package Count';
            ToolTip = 'Specifies the package count.';
        }
        field(7; "Package Quantity"; Decimal)
        {
            Caption = 'Package Quantity';
            ToolTip = 'Specifies the package quantity.';
        }
        field(8; "Creation Method"; Enum "Package Creation Method MXW")
        {
            Caption = 'Creation Method';
            ToolTip = 'Specifies the package creation method.';
            trigger OnValidate()
            begin
                case Rec."Creation Method" of
                    Rec."Creation Method"::Single:
                        begin
                            Rec.Validate("Package Count", 1);
                            Rec.Validate("Package Quantity", 0);
                        end;
                    Rec."Creation Method"::Multiple:
                        begin
                            Rec.Validate("Package Count", 0);
                            Rec.Validate("Package Quantity", 0);
                        end;
                end;
            end;
        }
        field(9; "Source Type"; Enum "Package Creation Src. Type MXW")
        {
            Caption = 'Source Type';
            ToolTip = 'Specifies the source type of the package creation.';
            Editable = false;
        }
        field(10; "Item Pieces Of Pallet"; Decimal)
        {
            Caption = 'Item Pieces Of Pallet';
            ToolTip = 'Specifies the number of pieces per pallet.';
            AllowInCustomizations = Always;
        }
        field(11; "Order Quantity"; Decimal)
        {
            Caption = 'Order Quantity';
            ToolTip = 'Specifies the order quantity.';
        }
        field(12; "Remaining Quantity"; Decimal)
        {
            Caption = 'Remaining Quantity';
            ToolTip = 'Specifies the remaining quantity.';
            AllowInCustomizations = Always;
        }
        field(13; "Max Available Quantity"; Decimal)
        {
            Caption = 'Max Available Quantity';
            ToolTip = 'Specifies the maximum available quantity.';
        }
        field(14; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the variant code.';
            trigger OnValidate()
            var
                BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
            begin
                if ("Variant Code" <> '') and ("Item No." <> '') then
                    Rec."Item Description" := BasicFuncs.GetItemDescription("Item No.", "Variant Code");
            end;
        }
        field(15; "Expiration Date"; Date)
        {
            Caption = 'Expiration Date';
            ToolTip = 'Specifies the expiration date.';
        }
        field(16; "Purchase Order No."; Code[20])
        {
            Caption = 'Purchase Order No.';
            ToolTip = 'Specifies the purchase order number.';
            Editable = false;
        }
        field(17; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            ToolTip = 'Specifies the unit of measure code.';
            TableRelation = "Unit of Measure".Code;
        }
    }

    keys
    {
        key(PK; "Document No.", "Document Line No.")
        {
            Clustered = true;
        }
    }
}
