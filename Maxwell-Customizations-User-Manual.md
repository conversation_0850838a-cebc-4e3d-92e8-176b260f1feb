# Maxwell Customizations for Business Central - User Manual

**Version:** ********
**Publisher:** Infotek Yazilim ve Donanim A.S.
**Date:** August 2025

---

## Table of Contents

1. [Overview](#overview)
2. [Getting Started](#getting-started)
3. [Core Modules](#core-modules)
4. [Purchase Management Workflows](#purchase-management-workflows)
5. [Production Management Workflows](#production-management-workflows)
6. [Sales & Warehouse Shipment Workflows](#sales--warehouse-shipment-workflows)
7. [Package Transfer Management](#package-transfer-management)
8. [Quality Control Integration](#quality-control-integration)
9. [Reporting & Label Printing](#reporting--label-printing)
10. [Setup & Configuration](#setup--configuration)
11. [Troubleshooting](#troubleshooting)
12. [Appendix](#appendix)

---

## Overview

The Maxwell Customizations extension enhances Microsoft Dynamics 365 Business Central with comprehensive **package-centric warehouse management** capabilities. This extension is specifically designed for manufacturing and distribution companies that need to track individual packages throughout their supply chain, from purchase receipt to sales shipment, with integrated quality control and production tracking.

### Key Features

- **Package-Based Inventory Management**: Track individual packages with unique barcodes throughout your operations
- **Lot Tracking Integration**: Enhanced lot number management with expiration date tracking
- **Quality Control Workflow**: Integrated quality control document processing for purchase receipts
- **Barcode-Driven Operations**: Scan packages for efficient warehouse operations
- **Production Package Creation**: Create packages directly from production orders
- **Package Transfer Orders**: Transfer packages between locations with full traceability
- **Label Printing**: Automated pallet label generation with barcode/QR codes
- **Expiration Date Management**: Prevent usage of expired lots and packages

### Business Domains Covered

- **Purchasing**: Enhanced warehouse receipt processing with package creation
- **Production**: Package creation from production orders with output tracking
- **Sales**: Package-based warehouse shipment processing
- **Warehouse Management**: Package tracking across locations
- **Quality Control**: Integration with Quality Control Management extension

---

## Getting Started

### Prerequisites

Before using the Maxwell Customizations, ensure you have:

1. **Microsoft Dynamics 365 Business Central** (version 26.0 or later)
2. **Quality Control Management extension** (version ********) - Required dependency
3. **G/L Account Names in Additional Languages extension** (version *********) - Required dependency
4. Appropriate user permissions (see [Security Setup](#security-setup))

### Installation & Setup

1. **Install the Extension**: Install the Maxwell Customizations extension through the Extension Management page
2. **Configure Setup**: Navigate to **Maxwell Setup** page to configure number series and journal templates
3. **Assign Permissions**: Assign the "Maxwell Perm MXW" permission set to users
4. **Configure Items**: Set up "Packages Per Pallet" for relevant items

### Navigation

Access Maxwell functionality through:
- **Purchase Order** → Functions menu (package creation)
- **Warehouse Receipt** → Functions menu (item tracking assignment)
- **Warehouse Shipment** → Barcode reading and functions
- **Production Orders** → Package creation from production lines
- **Package Transfer Orders** → New document type for package transfers

---

## Core Modules

### 1. Package Management Core (`MaxwellBasicFunctionsMXW`)

**Purpose**: Provides core utilities for package and lot management across the system.

**Key Functions**:
- User lookup by Security ID
- Item description resolution (item + variant)
- Lot expiration validation
- Package expiration validation
- User confirmation dialogs for expired inventory

**Usage**: Automatically used by other modules - no direct user interaction required.

### 2. Purchase Management (`MaxwellPurchaseMngtMXW`)

**Purpose**: Manages package creation from warehouse receipts and purchase processing.

**Key Responsibilities**:
- Package creation from warehouse receipt lines
- Quality control document generation
- Item tracking assignment
- Purchase pallet label printing

### 3. Production Management (`MaxwellProductionMngtMXW`)

**Purpose**: Handles package creation from production orders and output journal management.

**Key Responsibilities**:
- Package creation from production order lines
- Output journal creation and posting
- Lot number assignment for production items
- Production package label printing

### 4. Sales Management (`MaxwellSalesMngtMXW`)

**Purpose**: Manages package-based warehouse shipments and sales processing.

**Key Responsibilities**:
- Barcode scanning for warehouse shipments
- Item tracking assignment for sales
- Package validation and expiration checking

### 5. Package Transfer Management (`MaxwellPackageTransMgtMXW`)

**Purpose**: Handles inter-location package transfers with full traceability.

**Key Responsibilities**:
- Package transfer order processing
- Barcode scanning for package selection
- Item reclassification journal creation and posting

---

## Purchase Management Workflows

### Warehouse Receipt Processing

The purchase workflow centers around enhanced warehouse receipt processing with package creation capabilities.

#### 1. Create Warehouse Receipt

1. Create Purchase Order
2. Complete Order Related Processes
3. Click **Create Warehouse Receipt**
3. The system automatically:
   - Populates vendor information from purchase orders
   - Assigns lot numbers to items (if not already assigned)
   - Creates quality control documents for tracked items

#### 2. Warehouse Receipt Line Setup

For each warehouse receipt line, you can:

**Required Fields**:
- **Lot No. MXW**: Automatically assigned or manually entered
- **Expiration Date MXW**: Required for items with mandatory expiration date tracking

**Quality Control Integration**:
- **Quality Control Doc. No. MXW**: Automatically created
- **Quality Control Doc. Stat. MXW**: Shows current status (Input Pending, Acceptance, etc.)

#### 3. Package Creation Process

**Single Package Creation**:
1. Select a warehouse receipt line
2. Click **Functions** → **Create Package**
3. In the Package Creation dialog:
   - Verify item information
   - Set **Creation Method** to "Single"
   - Enter **Package Quantity**
   - Confirm creation
4. System creates package with unique package number
5. Automatically prints purchase pallet label

**Multiple Package Creation**:
1. Follow steps 1-2 above
2. Set **Creation Method** to "Multiple"
3. Enter **Package Count** and **Package Quantity per package**
4. System validates total quantity doesn't exceed available quantity
5. Creates multiple packages with sequential numbers

#### 4. Item Tracking Assignment

**Automatic Assignment**:
1. Click **Functions** → **Assign Item Tracking Info.**
2. System automatically assigns lot numbers and package numbers to purchase lines
3. Updates **Qty. to Receive** based on package quantities

### Purchase Order Integration

The system enhances standard purchase orders with package-related information:

- **Package tracking**: Links packages back to original purchase orders
- **Vendor integration**: Automatic vendor assignment to warehouse receipts
- **Quality requirements**: Validates QC status before allowing package operations

---

## Production Management Workflows

### Production Order Package Creation

Production orders can generate packages directly from output production, enabling full traceability from raw materials to finished goods.

#### 1. Production Order Setup

1. Create or release a production order
2. System automatically sets **Default Output Location** if configured in setup
3. For each production order line:
   - **Lot No. MXW**: Automatically assigned from item's lot number series
   - **Expiration Date MXW**: Must be specified

#### 2. Package Creation from Production

1. Navigate to **Released Production Order Lines**
2. Select a production line
3. Click **Functions** → **Create Package**
4. In the Package Creation dialog:
   - **Source Type** shows "Production"
   - Verify item and lot information
   - Set package quantities based on production output
   - **Item Pieces Of Pallet**: Defaults from item card

#### 3. Production Order Line Details

The system creates **Prod. Order Line Detail** records for each package:
- Links package to specific production order and line
- Tracks package quantity and location
- Maintains posted status

**Key Actions**:
- **Create Output Journals**: Generates output journals for selected packages
- **Print Palette Label**: Prints production labels with barcode/QR codes

#### 4. Output Journal Integration

1. Select package details from production order line
2. Click **Create Output Journals**
3. System automatically:
   - Creates output journal lines
   - Assigns lot numbers and package numbers via item tracking
   - Sets output quantities based on package quantities
   - Opens Output Journal page for review and posting

#### 5. BOM Management

**Convert Production BOM to Assembly BOM**:
1. Navigate to **Item Card** for finished good
2. Click **Maxwell Functions** → **Create Assembly BOM from Production BOM**
3. System converts production BOM components to assembly BOM components
4. Maintains component relationships and quantities

---

## Sales & Warehouse Shipment Workflows

### Package-Based Warehouse Shipments

Sales processing utilizes barcode scanning to efficiently select packages for shipment.

#### 1. Warehouse Shipment Creation

1. Create warehouse shipment from sales orders using standard BC process
2. Warehouse shipment lines show standard item information
3. Maxwell extensions add package tracking capabilities

#### 2. Barcode Package Selection

**Using Barcode Scanning**:
1. Open the warehouse shipment document
2. Focus on **Barcode Text** field in the Barcode Reading section
3. Scan or enter package barcode
4. System automatically:
   - Validates package exists and is available
   - Checks for expiration and prompts user if expired
   - Prevents duplicate package selection
   - Creates warehouse shipment line detail
   - Clears barcode field for next scan

**Validation Rules**:
- Package must exist in Package No. Information
- Package cannot already be assigned to this shipment
- Package must not be already shipped
- Package must match item on warehouse shipment line
- Expired packages require user confirmation

#### 3. Warehouse Shipment Line Details

Each scanned package creates a **Warehouse Shipment Line Detail**:
- Links package to specific shipment line
- Shows package quantity and tracking information
- Tracks shipped status
- Maintains sales order reference

**View Details**:
1. From warehouse shipment, click **Maxwell** → **Warehouse Shipment Line Details**
2. Review all packages assigned to the shipment
3. Monitor shipped status

#### 4. Item Tracking Assignment

**Assign Tracking Information**:
1. Click **Functions** → **Assign Item Tracking Info.**
2. System processes all unassigned packages
3. Creates item tracking lines for sales orders
4. Updates **Qty. to Ship** based on total package quantities

#### 5. Shipment Processing

After scanning packages and assigning item tracking:
1. Post the warehouse shipment using standard BC functionality
2. System automatically updates **Shipped** status on package details
3. Package tracking follows standard BC item ledger entry creation

---

## Package Transfer Management

### Inter-Location Package Transfers

Package Transfer Orders enable moving packages between locations while maintaining full traceability.

#### 1. Create Package Transfer Order

1. Navigate to **Package Transfer Orders**
2. Create new transfer order
3. Set **Transfer-to Code** (destination location)
4. **Transfer-from Code** is automatically populated when first package is scanned

#### 2. Package Selection via Barcode

**Barcode Scanning Process**:
1. Focus on **Barcode** field
2. Scan or enter package number
3. System automatically:
   - Validates package exists
   - Sets Transfer-from Code if not already set
   - Validates package location matches Transfer-from Code
   - Creates transfer line with package details
   - Clears barcode for next scan

**Package Transfer Lines**:
- **Package No.**: Scanned package number
- **Quantity**: Package inventory quantity
- **Quantity To Transfer**: Editable quantity (default = full quantity)
- **Transfer-from Code**: Source location
- **Current Package Location**: FlowField showing actual package location

#### 3. Transfer Execution

**Ship & Receive (Single Step)**:
1. Click **Ship & Receive** action
2. System validates all packages have quantity > 0 to transfer
3. Creates and posts item reclassification journal
4. Updates both **Shipped** and **Received** status
5. Displays success message

**Two-Step Process** (for production locations):
1. Click **Ship** to mark as shipped
2. Later, click **Receive** to complete transfer
3. Creates item reclassification journal on receive step

#### 4. Transfer Validation

**Business Rules**:
- Transfer-from and Transfer-to locations cannot be the same
- All packages must have Transfer-to Code specified
- Package must be available at Transfer-from location
- Quantity to Transfer cannot exceed package quantity

**Item Journal Creation**:
- Uses configured Package Transfer Journal Template and Batch
- Creates Transfer entry type with lot and package tracking
- Maintains lot numbers through transfer (no lot conversion)

---

## Quality Control Integration

### Quality Control Workflow

The extension integrates with the Quality Control Management extension to ensure quality compliance before package operations.

#### 1. Automatic QC Document Creation

When warehouse receipt lines are created:
1. System automatically generates Quality Control documents
2. Links QC document to specific lot and item combination
3. Sets initial status to "Input Pending"
4. Populates vendor information from purchase order

#### 2. Quality Control Processing

**QC Document Management**:
- Each unique combination of Item No. + Lot No. gets one QC document
- Multiple warehouse receipt lines can share the same QC document
- QC status prevents package creation until processed

**Status Validation**:
- **Package creation blocked** when QC status is blank or "Input Pending"
- **Package creation allowed** when QC status is "Acceptance"
- Error message guides users to process QC documents first

#### 3. Bulk QC Processing

**Accept All Quality Control Documents**:
1. From warehouse receipt header
2. Click **Functions** → **Accept All Quality Control Documents**
3. System finds all related QC documents
4. Bulk updates status to "Acceptance"
5. Enables package creation for all lines

#### 4. QC Status Tracking

**Warehouse Receipt Line Fields**:
- **Quality Control Documents MXW**: Count of QC documents
- **Quality Control Doc. No. MXW**: Specific QC document number
- **Quality Control Doc. Stat. MXW**: Current QC status

**Quality Control Integration**:
- Leverages QCM extension enums and tables
- Maintains data consistency with QC processes
- Supports existing QC workflows and approval processes

---

## Reporting & Label Printing

### Label Generation System

The extension provides comprehensive label printing for packages and pallets with barcode integration.

#### 1. Purchase Pallet Labels

**Automatic Printing**:
- Triggered automatically after package creation from warehouse receipts
- Prints labels for all newly created packages in batch

**Manual Printing**:
1. Navigate to **Warehouse Receipt Line Details**
2. Select desired package lines
3. Click **Print Purchase Pallet Label**
4. Report includes:
   - Package number and barcode
   - Item information and description
   - Quantities and lot information
   - Purchase order reference
   - Expiration date
   - Created by and checked by information

#### 2. Production Palette Labels

**Production Package Labels**:
1. From **Prod. Order Line Details** page
2. Select production packages
3. Click **Print Palette Label**
4. System:
   - Filters Package No. Information records
   - Generates labels with production-specific information
   - Includes barcode and QR code generation

**Label Features**:
- **Barcode Generation**: Code128 format for package numbers
- **QR Code Generation**: 2D barcode for enhanced scanning
- **Production References**: Links to production order numbers
- **Expiration Tracking**: Shows expiration dates from lot information

#### 3. Label Content & Formatting

**Standard Label Elements**:
- Package number (human readable)
- Package number barcode (Code128)
- Package number QR code
- Item number and description
- Lot number and expiration date
- Quantity and unit of measure
- Location code
- Document references (purchase/production order)
- Created by user information
- Created/modified timestamps

**Customization Options**:
- RDLC layouts in `src/reportlayout/` folder
- Multiple label format variations available
- Barcode font provider integration for reliable printing

---

## Setup & Configuration

### Maxwell Setup Page

Navigate to **Maxwell Setup** to configure the extension.

#### 1. Warehouse Receipt Setup

**Package Transfer Configuration**:
- **Package Transfer Nos. MXW**: Number series for package transfer documents
- **Package Tran. Jnl. Temp. MXW**: Item journal template for package transfers
- **Package Tran. Jnl. Batch MXW**: Item journal batch for package transfers

#### 2. Production Setup

**Journal Configuration**:
- **Consumption Jnl. Template MXW**: Template for consumption journals
- **Consumption Jnl. Batch MXW**: Batch for consumption journals
- **Output Journal Template MXW**: Template for output journals
- **Output Journal Batch MXW**: Batch for output journals

**Location Setup**:
- **Default Output Location Code**: Automatically assigned to new production orders

#### 3. Item Setup

**Item Card Extensions**:
- **Packages Per Pallet MXW**: Default package count for production packages
- **Unit per Parcel MXW**: Units per parcel for packaging calculations

#### 4. Number Series Configuration

**Required Number Series**:
- **Package Nos.**: From Inventory Setup (standard BC)
- **Package Transfer Nos. MXW**: From Maxwell Setup
- **Quality Control Nos.**: From Quality Control Management Setup

#### 5. Journal Template Setup

**Create Required Templates** (if not existing):
1. **Item Journal Templates**:
   - Transfer template for package transfers
   - Output template for production journals
   - Consumption template for production consumption

2. **Item Journal Batches**:
   - Create batches under each template
   - Link batches in Maxwell Setup

### Security Setup

#### Permission Set: "Maxwell Perm MXW"

**Included Permissions**:
- Full access (RIMD) to all Maxwell tables
- Execute (X) permissions for Maxwell objects
- Integration with Quality Control Management tables

**Assignment**:
1. Navigate to **Users** or **User Groups**
2. Assign **Maxwell Perm MXW** permission set
3. Ensure users also have standard BC warehouse and production permissions

#### User Setup Requirements

**Additional Required Permissions**:
- Warehouse Management permissions for receipt/shipment processing
- Production Order permissions for production package creation
- Item Journal permissions for package transfers
- Quality Control Management permissions (if using QC features)

### Integration Setup

#### Quality Control Management

**Prerequisites**:
- Quality Control Management extension installed and configured
- QC number series configured
- QC document types and approval workflows setup

**Integration Points**:
- Automatic QC document creation from warehouse receipts
- QC status validation before package creation
- Bulk QC approval functionality

#### Standard BC Integration

**Item Tracking Code Setup**:
- Configure lot tracking for relevant items
- Set expiration date requirements
- Package tracking automatically inherits from lot tracking

---

## Troubleshooting

### Common Issues & Solutions

#### Package Creation Issues

**Error: "You cannot create package without processing the corresponding line's quality control document"**
- **Cause**: QC document status is blank or "Input Pending"
- **Solution**: Process QC documents or use "Accept All Quality Control Documents" function

**Error: "Expiration Date is mandatory for item [X] based on its Item Tracking Code settings"**
- **Cause**: Item tracking code requires expiration date but field is empty
- **Solution**: Enter expiration date on warehouse receipt line before package creation

**Error: "Package count exceeded"**
- **Cause**: Total package quantity exceeds available quantity on warehouse receipt line
- **Solution**: Reduce package count or quantity, or verify warehouse receipt line quantity

#### Package Transfer Issues

**Error: "Transfer-from and Transfer-to locations cannot be the same"**
- **Cause**: Both locations are identical
- **Solution**: Set different transfer-to location before shipping

**Error: "Package No.: [X] not found"**
- **Cause**: Scanned package number doesn't exist in Package No. Information
- **Solution**: Verify package number or ensure package was created properly

**Error: "This package has already been read in this package transfer order"**
- **Cause**: Attempting to scan the same package twice
- **Solution**: Skip this package or remove previous transfer line

#### Production Package Issues

**Error: "Item [X] not found"**
- **Cause**: Production order line references non-existent item
- **Solution**: Verify item number or refresh production order

**Error: "You exceeded the production order Max Available Quantity"**
- **Cause**: Package quantities exceed production order quantity
- **Solution**: Reduce package count/quantity or verify production order quantities

#### Barcode Scanning Issues

**Problem**: Barcode scanner not working
- **Solution**: Ensure barcode field has focus, check scanner configuration, verify USB/wireless connection

**Problem**: Wrong packages being selected
- **Solution**: Verify barcode symbology matches (Code128), check package number format

### Data Integrity

#### Validation Rules

**Package Uniqueness**:
- Package numbers must be unique across all items
- System validates package existence before operations
- Package-to-lot relationships maintained automatically

**Quantity Consistency**:
- Package quantities must match item ledger entries
- System validates available quantities before transfers
- Inventory calculations include package-level tracking

#### Backup Considerations

**Export Capabilities**:
- Use BC standard data export for package information
- Maintain package label printing capabilities for reprints

---

## Appendix

### Object Reference

#### Tables (60000-60999)
- `60000` Maxwell Setup MXW
- `60001` Package Creation MXW (Temporary)
- `60002` Warehouse Receipt Line Dtl MXW
- `60003` Whse. Shipment Line Dtl. MXW
- `60011` Prod. Order Line Detail MXW
- `60020` Package Transfer Header MXW
- `60021` Package Transfer Line MXW

#### Pages (60000-60999)
- `60000` Maxwell Setup MXW
- `60001` Package Creation MXW (Dialog)
- `60002` Whse. Receipt Line Details MXW
- `60003` Whse. Shipment Line Dtl MXW
- `60011` Prod. Order Line Details MXW
- `60022` Package Transfer Order MXW
- `60023` Package Transfer Orders MXW

#### Codeunits (60000-60999)
- `60000` Maxwell Events MXW
- `60001` Maxwell Purchase Mngt. MXW
- `60002` Maxwell Production Mngt.MXW
- `60004` Maxwell Sales Mngt. MXW
- `60006` Secondary CoA Management MXW
- `60007` Maxwell Package Trans. Mgt MXW
- `60010` Maxwell Basic Functions MXW
- `60011` Maxwell BOM Management MXW

#### Reports (60000-60999)
- `60000` Purchase Pallet Label MXW
- `60080` Palette Label MXW

#### Enums (60000-60999)
- `60000` Package Creation Method MXW
- `60001` Package Creation Src. Type MXW

### Field Reference

#### Key Extension Fields

**Warehouse Receipt Line MXW**:
- `60000` Package Count MXW (FlowField)
- `60001` Total Package Quantity MXW (FlowField)
- `60002` Lot No. MXW
- `60003` Item Tracking Info Assignd MXW
- `60004` Quality Control Documents MXW (FlowField)
- `60005` Quality Control Doc. No. MXW
- `60006` Quality Control Doc. Stat. MXW (FlowField)
- `60007` Expiration Date MXW

**Package No. Information MXW**:
- `60000` Lot No. MXW
- `60001` Location Code MXW (FlowField)
- `60003` Document No. MXW
- `60004` Expiration Date MXW
- `60005` Label Quantity MXW

**Item MXW**:
- `60000` Packages Per Pallet MXW


### Barcode Specifications

#### Supported Formats
- **Code128**: Primary barcode format for package numbers
- **QR Code**: 2D barcode for enhanced information storage
- **ID Automation**: Font provider for barcode generation


### Version History

#### Version ******** (Current)
- Initial release for BC 26.0
- Core package management functionality
- Quality Control Management integration
- Production and purchase workflows
- Package transfer capabilities


---

**© 2025 Infotek Yazilim ve Donanim A.S. All rights reserved.**

*This manual covers Maxwell Customizations version ********. For support, contact your system administrator or Maxwell implementation partner.*
