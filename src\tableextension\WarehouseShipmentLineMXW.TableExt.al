tableextension 60008 "Warehouse Shipment Line MXW" extends "Warehouse Shipment Line"
{
    fields
    {
        field(60000; "Package Count MXW"; Integer)
        {
            Caption = 'Package Count';
            ToolTip = 'Specifies the package count.';
            FieldClass = FlowField;
            CalcFormula = count("Whse. Shipment Line Dtl. MXW" where("Document No." = field("No."), "Document Line No." = field("Line No.")));
            Editable = false;
        }
        field(60001; "Total Package Quantity MXW"; Decimal)
        {
            Caption = 'Total Package Quantity';
            ToolTip = 'Specifies the total package quantity.';
            FieldClass = FlowField;
            CalcFormula = sum("Whse. Shipment Line Dtl. MXW".Quantity where("Document No." = field("No."), "Document Line No." = field("Line No.")));
            Editable = false;
        }
        field(60002; "Item Tracking Info Assignd MXW"; Boolean)
        {
            Caption = 'Item Tracking Info. Assigned';
            ToolTip = 'Specifies whether the item tracking information is assigned.';
        }
    }
    // No SIFT key for FlowFields; handled in source table.
}
