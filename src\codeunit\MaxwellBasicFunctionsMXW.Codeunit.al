codeunit 60010 "Maxwell Basic Functions MXW"
{
    procedure GetUserFullNameFromSecurityId(UserSecurityID: Guid): Text[80]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."Full Name");
        exit('');
    end;

    procedure GetUserNameFromSecurityId(UserSecurityID: Guid): Code[50]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."User Name");
        exit('');
    end;

    // Returns a user-friendly description for an item and optional variant.
    // If VariantCode is empty, returns Item.Description; if variant exists, returns Variant.Description
    // Falls back to Item.Description when variant not found or descriptions are empty.
    procedure GetItemDescription(ItemNo: Code[20]; VariantCode: Code[10]): Text[100]
    var
        Item: Record Item;
        ItemVariant: Record "Item Variant";
        Desc: Text[100];
    begin
        // Try to get item first
        if not Item.Get(ItemNo) then
            exit('');

        // If variant code provided, try to get variant description
        if (VariantCode <> '') then
            if ItemVariant.Get(ItemNo, VariantCode) then begin
                // Prefer Variant Description if present
                Desc := ItemVariant.Description;
                if Desc <> '' then
                    exit(Desc);
            end;

        // Fallback to item description
        Desc := Item.Description;
        exit(Desc);
    end;

    // Checks if a lot is expired based on the expiration date in Lot No. Information
    // Returns true if the lot is expired (expiration date < today), false otherwise
    procedure IsLotExpired(ItemNo: Code[20]; VariantCode: Code[10]; LotNo: Code[50]): Boolean
    var
        LotNoInformation: Record "Lot No. Information";
    begin
        if LotNo = '' then
            exit(false);

        if not LotNoInformation.Get(ItemNo, VariantCode, LotNo) then
            exit(false);

        // If expiration date is not set, consider not expired
        if LotNoInformation."Expiration Date MXW" = 0D then
            exit(false);

        // Check if expiration date is before today
        exit(LotNoInformation."Expiration Date MXW" < Today());
    end;

    // Checks if a package is expired based on the expiration date in Package No. Information
    // Returns true if the package is expired (expiration date < today), false otherwise
    procedure IsPackageExpired(ItemNo: Code[20]; VariantCode: Code[10]; PackageNo: Code[50]): Boolean
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        if PackageNo = '' then
            exit(false);

        if not PackageNoInformation.Get(ItemNo, VariantCode, PackageNo) then
            exit(false);

        // If expiration date is not set, consider not expired
        if PackageNoInformation."Expiration Date MXW" = 0D then
            exit(false);

        // Check if expiration date is before today
        exit(PackageNoInformation."Expiration Date MXW" < Today());
    end;

    // Prompts user with a confirmation dialog if lot is expired
    // Returns true if user wants to continue, false if process should be aborted
    procedure ConfirmExpiredLotUsage(ItemNo: Code[20]; VariantCode: Code[10]; LotNo: Code[50]): Boolean
    var
        LotNoInformation: Record "Lot No. Information";
        ConfirmManagement: Codeunit "Confirm Management";
        ConfirmExpiredLotMsg: Label 'Lot No. %1 for Item %2 has expired on %3. Do you want to continue processing with expired inventory?', Comment = '%1=Lot No., %2=Item No., %3=Expiration Date';
        ProcessAbortedErr: Label 'Process aborted due to expired inventory selection.';
    begin
        if not IsLotExpired(ItemNo, VariantCode, LotNo) then
            exit(true);

        if LotNoInformation.Get(ItemNo, VariantCode, LotNo) then
            if not ConfirmManagement.GetResponseOrDefault(StrSubstNo(ConfirmExpiredLotMsg, LotNo, ItemNo, LotNoInformation."Expiration Date MXW"), false) then
                Error(ProcessAbortedErr);

        exit(true);
    end;

    // Prompts user with a confirmation dialog if package is expired
    // Returns true if user wants to continue, false if process should be aborted
    procedure ConfirmExpiredPackageUsage(ItemNo: Code[20]; VariantCode: Code[10]; PackageNo: Code[50]): Boolean
    var
        PackageNoInformation: Record "Package No. Information";
        ConfirmManagement: Codeunit "Confirm Management";
        ConfirmExpiredPackageMsg: Label 'Package No. %1 for Item %2 has expired on %3. Do you want to continue processing with expired inventory?', Comment = '%1=Package No., %2=Item No., %3=Expiration Date';
        ProcessAbortedErr: Label 'Process aborted due to expired inventory selection.';
    begin
        if not IsPackageExpired(ItemNo, VariantCode, PackageNo) then
            exit(true);

        if PackageNoInformation.Get(ItemNo, VariantCode, PackageNo) then
            if not ConfirmManagement.GetResponseOrDefault(StrSubstNo(ConfirmExpiredPackageMsg, PackageNo, PackageNoInformation."Item No.", PackageNoInformation."Expiration Date MXW"), false) then
                Error(ProcessAbortedErr);

        exit(true);
    end;

    // Calculates expiration date for an item based on receipt date and item's expiration settings
    // Uses Expiration Date Formula MXW if specified, otherwise uses Default Shelf Life Days MXW
    // Returns 0D if no expiration settings are configured for the item
    procedure CalculateExpirationDate(ItemNo: Code[20]; ReceiptDate: Date): Date
    var
        Item: Record Item;
        ExpirationDate: Date;
    begin
        if not Item.Get(ItemNo) then
            exit(0D);

        if ReceiptDate = 0D then
            ReceiptDate := Today();

        // First try to use the date formula if specified
        if Format(Item."Expiration Date Formula MXW") <> '' then begin
            ExpirationDate := CalcDate(Item."Expiration Date Formula MXW", ReceiptDate);
            exit(ExpirationDate);
        end;

        // Otherwise use default shelf life days if specified
        if Item."Default Shelf Life Days MXW" > 0 then begin
            ExpirationDate := ReceiptDate + Item."Default Shelf Life Days MXW";
            exit(ExpirationDate);
        end;

        // No expiration settings configured
        exit(0D);
    end;

    // Calculates expiration date and updates warehouse receipt line
    // Prioritizes Lot No. Information as the primary source for expiration tracking
    // Also updates related package information to maintain consistency
    procedure UpdateWarehouseReceiptLineExpirationDate(var WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        LotNoInformation: Record "Lot No. Information";
        PackageNoInformation: Record "Package No. Information";
        WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW";
        CalculatedExpirationDate: Date;
    begin
        if WarehouseReceiptLine."Item No." = '' then
            exit;

        // Calculate expiration date based on item settings
        CalculatedExpirationDate := CalculateExpirationDate(WarehouseReceiptLine."Item No.", Today());

        if CalculatedExpirationDate = 0D then
            exit;

        // Update warehouse receipt line
        WarehouseReceiptLine."Expiration Date MXW" := CalculatedExpirationDate;
        //WarehouseReceiptLine.Modify(false);

        // Update lot information if lot exists
        if WarehouseReceiptLine."Lot No. MXW" <> '' then
            if LotNoInformation.Get(WarehouseReceiptLine."Item No.", WarehouseReceiptLine."Variant Code", WarehouseReceiptLine."Lot No. MXW") then begin
                LotNoInformation."Expiration Date MXW" := CalculatedExpirationDate;
                LotNoInformation.Modify(true);
            end;

        // Update package information for all related packages
        WarehouseReceiptLineDtl.SetRange("Document No.", WarehouseReceiptLine."No.");
        WarehouseReceiptLineDtl.SetRange("Document Line No.", WarehouseReceiptLine."Line No.");
        if WarehouseReceiptLineDtl.FindSet() then
            repeat
                if PackageNoInformation.Get(WarehouseReceiptLineDtl."Item No.", WarehouseReceiptLineDtl."Variant Code", WarehouseReceiptLineDtl."Package No.") then begin
                    PackageNoInformation."Expiration Date MXW" := CalculatedExpirationDate;
                    PackageNoInformation.Modify(true);
                end;
            until WarehouseReceiptLineDtl.Next() = 0;
    end;
}
