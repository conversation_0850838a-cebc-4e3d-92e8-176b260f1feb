codeunit 60010 "Maxwell Basic Functions MXW"
{
    procedure GetUserFullNameFromSecurityId(UserSecurityID: Guid): Text[80]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."Full Name");
        exit('');
    end;

    procedure GetUserNameFromSecurityId(UserSecurityID: Guid): Code[50]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."User Name");
        exit('');
    end;

    // Returns a user-friendly description for an item and optional variant.
    // If VariantCode is empty, returns Item.Description; if variant exists, returns Variant.Description
    // Falls back to Item.Description when variant not found or descriptions are empty.
    procedure GetItemDescription(ItemNo: Code[20]; VariantCode: Code[10]): Text[100]
    var
        Item: Record Item;
        ItemVariant: Record "Item Variant";
        Desc: Text[100];
    begin
        // Try to get item first
        if not Item.Get(ItemNo) then
            exit('');

        // If variant code provided, try to get variant description
        if (VariantCode <> '') then
            if ItemVariant.Get(ItemNo, VariantCode) then begin
                // Prefer Variant Description if present
                Desc := ItemVariant.Description;
                if Desc <> '' then
                    exit(Desc);
            end;

        // Fallback to item description
        Desc := Item.Description;
        exit(Desc);
    end;

    // Checks if a lot is expired based on the expiration date in Lot No. Information
    // Returns true if the lot is expired (expiration date < today), false otherwise
    procedure IsLotExpired(ItemNo: Code[20]; VariantCode: Code[10]; LotNo: Code[50]): Boolean
    var
        LotNoInformation: Record "Lot No. Information";
    begin
        if LotNo = '' then
            exit(false);

        if not LotNoInformation.Get(ItemNo, VariantCode, LotNo) then
            exit(false);

        // If expiration date is not set, consider not expired
        if LotNoInformation."Expiration Date MXW" = 0D then
            exit(false);

        // Check if expiration date is before today
        exit(LotNoInformation."Expiration Date MXW" < Today());
    end;

    // Checks if a package is expired based on the expiration date in Package No. Information
    // Returns true if the package is expired (expiration date < today), false otherwise
    procedure IsPackageExpired(ItemNo: Code[20]; VariantCode: Code[10]; PackageNo: Code[50]): Boolean
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        if PackageNo = '' then
            exit(false);

        if not PackageNoInformation.Get(ItemNo, VariantCode, PackageNo) then
            exit(false);

        // If expiration date is not set, consider not expired
        if PackageNoInformation."Expiration Date MXW" = 0D then
            exit(false);

        // Check if expiration date is before today
        exit(PackageNoInformation."Expiration Date MXW" < Today());
    end;

    // Prompts user with a confirmation dialog if lot is expired
    // Returns true if user wants to continue, false if process should be aborted
    procedure ConfirmExpiredLotUsage(ItemNo: Code[20]; VariantCode: Code[10]; LotNo: Code[50]): Boolean
    var
        LotNoInformation: Record "Lot No. Information";
        ConfirmManagement: Codeunit "Confirm Management";
        ConfirmExpiredLotMsg: Label 'Lot No. %1 for Item %2 has expired on %3. Do you want to continue processing with expired inventory?', Comment = '%1=Lot No., %2=Item No., %3=Expiration Date';
        ProcessAbortedErr: Label 'Process aborted due to expired inventory selection.';
    begin
        if not IsLotExpired(ItemNo, VariantCode, LotNo) then
            exit(true);

        if LotNoInformation.Get(ItemNo, VariantCode, LotNo) then
            if not ConfirmManagement.GetResponseOrDefault(StrSubstNo(ConfirmExpiredLotMsg, LotNo, ItemNo, LotNoInformation."Expiration Date MXW"), false) then
                Error(ProcessAbortedErr);

        exit(true);
    end;

    // Prompts user with a confirmation dialog if package is expired
    // Returns true if user wants to continue, false if process should be aborted
    procedure ConfirmExpiredPackageUsage(ItemNo: Code[20]; VariantCode: Code[10]; PackageNo: Code[50]): Boolean
    var
        PackageNoInformation: Record "Package No. Information";
        ConfirmManagement: Codeunit "Confirm Management";
        ConfirmExpiredPackageMsg: Label 'Package No. %1 for Item %2 has expired on %3. Do you want to continue processing with expired inventory?', Comment = '%1=Package No., %2=Item No., %3=Expiration Date';
        ProcessAbortedErr: Label 'Process aborted due to expired inventory selection.';
    begin
        if not IsPackageExpired(ItemNo, VariantCode, PackageNo) then
            exit(true);

        if PackageNoInformation.Get(ItemNo, VariantCode, PackageNo) then
            if not ConfirmManagement.GetResponseOrDefault(StrSubstNo(ConfirmExpiredPackageMsg, PackageNo, PackageNoInformation."Item No.", PackageNoInformation."Expiration Date MXW"), false) then
                Error(ProcessAbortedErr);

        exit(true);
    end;
}
