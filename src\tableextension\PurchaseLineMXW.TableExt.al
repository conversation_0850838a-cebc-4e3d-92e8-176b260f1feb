tableextension 60000 "Purchase Line MXW" extends "Purchase Line"
{
    fields
    {
        field(60000; "Whse. Rcpt. Qty-to Receive MXW"; Decimal)
        {
            Caption = 'Warehouse Receipt Qty-to Receive';
            ToolTip = 'Specifies the warehouse receipt quantity to receive.';
            DecimalPlaces = 0 : 5;
            AllowInCustomizations = Always;
            trigger OnValidate()
            var
                QuantityErr: Label '%1 cannot be greater than %2.', Comment = '%1=FieldCaption("Whse. Rcpt. Qty-to Receive MXW"); %2="Purchase Line"."Outstanding Quantity"';
            begin
                if Rec."Whse. Rcpt. Qty-to Receive MXW" > Rec."Outstanding Quantity" then
                    Error(QuantityErr, Rec.FieldCaption("Whse. Rcpt. Qty-to Receive MXW"), Rec."Outstanding Quantity");
            end;
        }
    }
}
