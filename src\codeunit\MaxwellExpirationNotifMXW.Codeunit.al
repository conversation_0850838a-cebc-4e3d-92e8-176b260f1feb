codeunit 60003 "Maxwell Expiration Notif. MXW"
{
    procedure SendExpirationNotifications(SkipFrequencyCheck: Boolean)
    var
        MaxwellSetup: Record "Maxwell Setup MXW";
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        EmailBody: Text;
        EmailSubject: Text;
        ExpiringItemsFound: Boolean;
        ShouldSendNotification: Boolean;
    begin
        MaxwellSetup.GetRecordOnce();

        // Check if notification is configured
        if (MaxwellSetup."Exp Notif Email MXW" = '') or
           (MaxwellSetup."Expiration Warning Days MXW" = 0) or
           (MaxwellSetup."Notification Frequency MXW" = MaxwellSetup."Notification Frequency MXW"::" ") then
            exit;

        // Check if we should send notification based on frequency (skip for testing)
        if not SkipFrequencyCheck then begin
            ShouldSendNotification := ShouldSendNotificationToday(MaxwellSetup);
            if not ShouldSendNotification then
                exit;
        end;

        // Build email content
        EmailSubject := 'Maxwell Foods - Expiration Date Warning';
        EmailBody := BuildExpirationNotificationEmail(MaxwellSetup, ExpiringItemsFound);

        if not ExpiringItemsFound then
            exit;

        // Send email
        EmailMessage.Create(MaxwellSetup."Exp Notif Email MXW", EmailSubject, EmailBody, true);
        Email.Send(EmailMessage);

        // Update last notification date (only when not testing)
        if not SkipFrequencyCheck then begin
            MaxwellSetup."Last Notification Date MXW" := Today();
            MaxwellSetup.Modify(true);
        end;
    end;

    /// <summary>
    /// Sends expiration notifications with frequency check (for job queue)
    /// </summary>
    procedure SendExpirationNotifications()
    begin
        SendExpirationNotifications(false);
    end;

    /// <summary>
    /// Sends test expiration notification email (skips frequency check)
    /// </summary>
    procedure SendTestExpirationNotification()
    begin
        SendExpirationNotifications(true);
    end;

    local procedure ShouldSendNotificationToday(MaxwellSetup: Record "Maxwell Setup MXW"): Boolean
    var
        DaysSinceLastNotification: Integer;
    begin
        if MaxwellSetup."Last Notification Date MXW" = 0D then
            exit(true);

        DaysSinceLastNotification := Today() - MaxwellSetup."Last Notification Date MXW";

        case MaxwellSetup."Notification Frequency MXW" of
            MaxwellSetup."Notification Frequency MXW"::Daily:
                exit(DaysSinceLastNotification >= 1);
            MaxwellSetup."Notification Frequency MXW"::Weekly:
                exit(DaysSinceLastNotification >= 7);
            MaxwellSetup."Notification Frequency MXW"::Monthly:
                exit(DaysSinceLastNotification >= 30);
            else
                exit(false);
        end;
    end;

    local procedure BuildExpirationNotificationEmail(MaxwellSetup: Record "Maxwell Setup MXW"; var ExpiringItemsFound: Boolean): Text
    var
        EmailBody: Text;
        WarningDate: Date;
    begin
        ExpiringItemsFound := false;
        WarningDate := Today() + MaxwellSetup."Expiration Warning Days MXW";

        EmailBody := '<html><body>';
        EmailBody += '<h2>Maxwell Foods - Expiration Date Warning</h2>';
        EmailBody += '<p><strong>Email Sent Date:</strong> ' + Format(Today(), 0, '<Day,2>/<Month,2>/<Year4>') + '</p>';
        EmailBody += '<p>The following lots are approaching their expiration dates within the next ' + Format(MaxwellSetup."Expiration Warning Days MXW") + ' days:</p>';

        // Process lots only (primary expiration tracking)
        EmailBody += BuildLotExpirationSection(WarningDate, ExpiringItemsFound);

        if not ExpiringItemsFound then
            EmailBody += '<p>No items are approaching expiration within the specified warning period.</p>';

        EmailBody += '<p><i>This notification was generated automatically by Maxwell Customizations.</i></p>';
        EmailBody += '</body></html>';

        exit(EmailBody);
    end;

    local procedure BuildLotExpirationSection(WarningDate: Date; var ExpiringItemsFound: Boolean): Text
    var
        LotNoInformation: Record "Lot No. Information";
        Item: Record Item;
        ItemLedgerEntry: Record "Item Ledger Entry";
        EmailBody: Text;
        LotCount: Integer;
        LotQuantity: Decimal;
        UnitOfMeasureCode: Code[10];
    begin
        LotNoInformation.SetFilter("Expiration Date MXW", '<=%1&>%2', WarningDate, Today());
        if not LotNoInformation.FindSet() then
            exit('');

        EmailBody += '<h3>Lots Approaching Expiration:</h3>';
        EmailBody += '<table border="1" style="border-collapse: collapse; width: 100%;">';
        EmailBody += '<tr><th>Item No.</th><th>Item Description</th><th>Lot No.</th><th>Quantity</th><th>Unit of Measure</th><th>Expiration Date</th><th>Days Until Expiration</th></tr>';

        repeat
            if Item.Get(LotNoInformation."Item No.") then begin
                // Calculate remaining quantity for this lot
                LotQuantity := 0;
                UnitOfMeasureCode := Item."Base Unit of Measure";

                ItemLedgerEntry.SetRange("Item No.", LotNoInformation."Item No.");
                ItemLedgerEntry.SetRange("Variant Code", LotNoInformation."Variant Code");
                ItemLedgerEntry.SetRange("Lot No.", LotNoInformation."Lot No.");
                ItemLedgerEntry.CalcSums("Remaining Quantity");
                LotQuantity := ItemLedgerEntry."Remaining Quantity";

                // Only show lots that have remaining quantity
                if LotQuantity > 0 then begin
                    EmailBody += '<tr>';
                    EmailBody += '<td>' + LotNoInformation."Item No." + '</td>';
                    EmailBody += '<td>' + Item.Description + '</td>';
                    EmailBody += '<td>' + LotNoInformation."Lot No." + '</td>';
                    EmailBody += '<td>' + Format(LotQuantity) + '</td>';
                    EmailBody += '<td>' + UnitOfMeasureCode + '</td>';
                    EmailBody += '<td>' + Format(LotNoInformation."Expiration Date MXW") + '</td>';
                    EmailBody += '<td>' + Format(LotNoInformation."Expiration Date MXW" - Today()) + '</td>';
                    EmailBody += '</tr>';
                    LotCount += 1;
                    ExpiringItemsFound := true;
                end;
            end;
        until LotNoInformation.Next() = 0;

        EmailBody += '</table>';
        EmailBody += '<p>Total lots: ' + Format(LotCount) + '</p>';

        exit(EmailBody);
    end;

    /// <summary>
    /// Creates a job queue entry for automatic expiration notifications
    /// </summary>
    procedure CreateJobQueueEntry()
    var
        JobQueueEntry: Record "Job Queue Entry";
        MaxwellSetup: Record "Maxwell Setup MXW";
    begin
        MaxwellSetup.GetRecordOnce();

        // Check if job queue entry already exists
        JobQueueEntry.SetRange("Object Type to Run", JobQueueEntry."Object Type to Run"::Codeunit);
        JobQueueEntry.SetRange("Object ID to Run", Codeunit::"Maxwell Expiration Notif. MXW");
        if not JobQueueEntry.IsEmpty() then
            exit;

        // Create new job queue entry
        JobQueueEntry.Init();
        JobQueueEntry."Object Type to Run" := JobQueueEntry."Object Type to Run"::Codeunit;
        JobQueueEntry."Object ID to Run" := Codeunit::"Maxwell Expiration Notif. MXW";
        JobQueueEntry."Run in User Session" := false;
        JobQueueEntry.Description := 'Maxwell Expiration Notifications';

        // Set frequency based on setup
        case MaxwellSetup."Notification Frequency MXW" of
            MaxwellSetup."Notification Frequency MXW"::Daily:
                JobQueueEntry."Recurring Job" := true;
            MaxwellSetup."Notification Frequency MXW"::Weekly:
                JobQueueEntry."Recurring Job" := true;
            MaxwellSetup."Notification Frequency MXW"::Monthly:
                JobQueueEntry."Recurring Job" := true;
        end;

        JobQueueEntry."Earliest Start Date/Time" := CreateDateTime(Today() + 1, 080000T); // 8:00 AM tomorrow
        JobQueueEntry.Status := JobQueueEntry.Status::Ready;
        JobQueueEntry.Insert(true);
    end;

    /// <summary>
    /// Shows the email content that would be sent for expiration notifications without actually sending the email
    /// </summary>
    procedure ShowEmailContentForTesting()
    var
        MaxwellSetup: Record "Maxwell Setup MXW";
        EmailBody: Text;
        EmailSubject: Text;
        ExpiringItemsFound: Boolean;
        ShouldSendNotification: Boolean;
        TestResultMsg: Label 'Email Subject: %1\\Email Body (HTML):\\%2', Comment = '%1=Email Subject, %2=Email Body';
        NoNotificationMsg: Label 'No notification would be sent. Reasons could be:\\- No email configured\\- No warning days configured\\- No notification frequency set\\- Frequency condition not met\\- No expiring items found';
    begin
        MaxwellSetup.GetRecordOnce();

        // Check if notification is configured
        if (MaxwellSetup."Exp Notif Email MXW" = '') or
           (MaxwellSetup."Expiration Warning Days MXW" = 0) or
           (MaxwellSetup."Notification Frequency MXW" = MaxwellSetup."Notification Frequency MXW"::" ") then begin
            Message(NoNotificationMsg);
            exit;
        end;

        // Check if we should send notification based on frequency
        ShouldSendNotification := this.ShouldSendNotificationToday(MaxwellSetup);
        if not ShouldSendNotification then begin
            Message('Notification frequency condition not met. Last notification: %1', MaxwellSetup."Last Notification Date MXW");
            exit;
        end;

        // Build email content
        EmailSubject := 'Maxwell Foods - Expiration Date Warning';
        EmailBody := this.BuildExpirationNotificationEmail(MaxwellSetup, ExpiringItemsFound);

        if not ExpiringItemsFound then begin
            Message('No expiring items found within %1 days.', MaxwellSetup."Expiration Warning Days MXW");
            exit;
        end;

        // Show the email content in a message
        Message(TestResultMsg, EmailSubject, EmailBody);
    end;
}
