codeunit 60003 "Maxwell Expiration Notif. MXW"
{
    procedure SendExpirationNotifications()
    var
        MaxwellSetup: Record "Maxwell Setup MXW";
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        EmailBody: Text;
        EmailSubject: Text;
        ExpiringItemsFound: Boolean;
        ShouldSendNotification: Boolean;
    begin
        MaxwellSetup.GetRecordOnce();

        // Check if notification is configured
        if (MaxwellSetup."Exp Notif Email MXW" = '') or
           (MaxwellSetup."Expiration Warning Days MXW" = 0) or
           (MaxwellSetup."Notification Frequency MXW" = MaxwellSetup."Notification Frequency MXW"::" ") then
            exit;

        // Check if we should send notification based on frequency
        ShouldSendNotification := ShouldSendNotificationToday(MaxwellSetup);
        if not ShouldSendNotification then
            exit;

        // Build email content
        EmailSubject := 'Maxwell Foods - Expiration Date Warning';
        EmailBody := BuildExpirationNotificationEmail(MaxwellSetup, ExpiringItemsFound);

        if not ExpiringItemsFound then
            exit;

        // Send email
        EmailMessage.Create(MaxwellSetup."Exp Notif Email MXW", EmailSubject, EmailBody, true);
        Email.Send(EmailMessage);

        // Update last notification date
        MaxwellSetup."Last Notification Date MXW" := Today();
        MaxwellSetup.Modify(true);
    end;

    local procedure ShouldSendNotificationToday(MaxwellSetup: Record "Maxwell Setup MXW"): Boolean
    var
        DaysSinceLastNotification: Integer;
    begin
        if MaxwellSetup."Last Notification Date MXW" = 0D then
            exit(true);

        DaysSinceLastNotification := Today() - MaxwellSetup."Last Notification Date MXW";

        case MaxwellSetup."Notification Frequency MXW" of
            MaxwellSetup."Notification Frequency MXW"::Daily:
                exit(DaysSinceLastNotification >= 1);
            MaxwellSetup."Notification Frequency MXW"::Weekly:
                exit(DaysSinceLastNotification >= 7);
            MaxwellSetup."Notification Frequency MXW"::Monthly:
                exit(DaysSinceLastNotification >= 30);
            else
                exit(false);
        end;
    end;

    local procedure BuildExpirationNotificationEmail(MaxwellSetup: Record "Maxwell Setup MXW"; var ExpiringItemsFound: Boolean): Text
    var
        EmailBody: Text;
        WarningDate: Date;
    begin
        ExpiringItemsFound := false;
        WarningDate := Today() + MaxwellSetup."Expiration Warning Days MXW";

        EmailBody := '<html><body>';
        EmailBody += '<h2>Maxwell Foods - Expiration Date Warning</h2>';
        EmailBody += '<p>The following lots are approaching their expiration dates within the next ' + Format(MaxwellSetup."Expiration Warning Days MXW") + ' days:</p>';

        // Process lots only (primary expiration tracking)
        EmailBody += BuildLotExpirationSection(WarningDate, ExpiringItemsFound);

        if not ExpiringItemsFound then
            EmailBody += '<p>No items are approaching expiration within the specified warning period.</p>';

        EmailBody += '<p><i>This notification was generated automatically by Maxwell Customizations.</i></p>';
        EmailBody += '</body></html>';

        exit(EmailBody);
    end;

    local procedure BuildLotExpirationSection(WarningDate: Date; var ExpiringItemsFound: Boolean): Text
    var
        LotNoInformation: Record "Lot No. Information";
        Item: Record Item;
        EmailBody: Text;
        LotCount: Integer;
    begin
        LotNoInformation.SetFilter("Expiration Date MXW", '<=%1&>%2', WarningDate, Today());
        if not LotNoInformation.FindSet() then
            exit('');

        EmailBody += '<h3>Lots Approaching Expiration:</h3>';
        EmailBody += '<table border="1" style="border-collapse: collapse; width: 100%;">';
        EmailBody += '<tr><th>Item No.</th><th>Item Description</th><th>Lot No.</th><th>Expiration Date</th><th>Days Until Expiration</th></tr>';

        repeat
            if Item.Get(LotNoInformation."Item No.") then begin
                EmailBody += '<tr>';
                EmailBody += '<td>' + LotNoInformation."Item No." + '</td>';
                EmailBody += '<td>' + Item.Description + '</td>';
                EmailBody += '<td>' + LotNoInformation."Lot No." + '</td>';
                EmailBody += '<td>' + Format(LotNoInformation."Expiration Date MXW") + '</td>';
                EmailBody += '<td>' + Format(LotNoInformation."Expiration Date MXW" - Today()) + '</td>';
                EmailBody += '</tr>';
                LotCount += 1;
                ExpiringItemsFound := true;
            end;
        until LotNoInformation.Next() = 0;

        EmailBody += '</table>';
        EmailBody += '<p>Total lots: ' + Format(LotCount) + '</p>';

        exit(EmailBody);
    end;

    local procedure BuildPackageExpirationSection(WarningDate: Date; var ExpiringItemsFound: Boolean): Text
    var
        PackageNoInformation: Record "Package No. Information";
        Item: Record Item;
        EmailBody: Text;
        PackageCount: Integer;
    begin
        PackageNoInformation.SetFilter("Expiration Date MXW", '<=%1&>%2', WarningDate, Today());
        if not PackageNoInformation.FindSet() then
            exit('');

        EmailBody += '<h3>Packages Approaching Expiration:</h3>';
        EmailBody += '<table border="1" style="border-collapse: collapse; width: 100%;">';
        EmailBody += '<tr><th>Item No.</th><th>Item Description</th><th>Package No.</th><th>Expiration Date</th><th>Days Until Expiration</th></tr>';

        repeat
            if Item.Get(PackageNoInformation."Item No.") then begin
                EmailBody += '<tr>';
                EmailBody += '<td>' + PackageNoInformation."Item No." + '</td>';
                EmailBody += '<td>' + Item.Description + '</td>';
                EmailBody += '<td>' + PackageNoInformation."Package No." + '</td>';
                EmailBody += '<td>' + Format(PackageNoInformation."Expiration Date MXW") + '</td>';
                EmailBody += '<td>' + Format(PackageNoInformation."Expiration Date MXW" - Today()) + '</td>';
                EmailBody += '</tr>';
                PackageCount += 1;
                ExpiringItemsFound := true;
            end;
        until PackageNoInformation.Next() = 0;

        EmailBody += '</table>';
        EmailBody += '<p>Total packages: ' + Format(PackageCount) + '</p>';

        exit(EmailBody);
    end;

    procedure CreateJobQueueEntry()
    var
        JobQueueEntry: Record "Job Queue Entry";
        MaxwellSetup: Record "Maxwell Setup MXW";
    begin
        MaxwellSetup.GetRecordOnce();

        // Check if job queue entry already exists
        JobQueueEntry.SetRange("Object Type to Run", JobQueueEntry."Object Type to Run"::Codeunit);
        JobQueueEntry.SetRange("Object ID to Run", Codeunit::"Maxwell Expiration Notif. MXW");
        if not JobQueueEntry.IsEmpty() then
            exit;

        // Create new job queue entry
        JobQueueEntry.Init();
        JobQueueEntry."Object Type to Run" := JobQueueEntry."Object Type to Run"::Codeunit;
        JobQueueEntry."Object ID to Run" := Codeunit::"Maxwell Expiration Notif. MXW";
        JobQueueEntry."Run in User Session" := false;
        JobQueueEntry.Description := 'Maxwell Expiration Notifications';

        // Set frequency based on setup
        case MaxwellSetup."Notification Frequency MXW" of
            MaxwellSetup."Notification Frequency MXW"::Daily:
                JobQueueEntry."Recurring Job" := true;
            MaxwellSetup."Notification Frequency MXW"::Weekly:
                JobQueueEntry."Recurring Job" := true;
            MaxwellSetup."Notification Frequency MXW"::Monthly:
                JobQueueEntry."Recurring Job" := true;
        end;

        JobQueueEntry."Earliest Start Date/Time" := CreateDateTime(Today() + 1, 080000T); // 8:00 AM tomorrow
        JobQueueEntry.Status := JobQueueEntry.Status::Ready;
        JobQueueEntry.Insert(true);
    end;
}
