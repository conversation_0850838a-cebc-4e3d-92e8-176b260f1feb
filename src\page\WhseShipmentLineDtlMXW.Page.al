page 60003 "Whse. Shipment Line Dtl MXW"
{
    ApplicationArea = All;
    Caption = 'Warehouse Shipment Line Details';
    PageType = List;
    SourceTable = "Whse. Shipment Line Dtl. MXW";
    UsageCategory = Lists;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Expiration Date"; Rec."Expiration Date")
                {
                }
                field("Sales Order No."; Rec."Sales Order No.")
                {
                }
                field("Item Tracking Info Assignd MXW"; Rec."Item Tracking Info Assignd MXW")
                {
                }
                field(Shipped; Rec.Shipped)
                {
                }
            }
        }
    }

    // actions
    // {
    //     area(Processing)
    //     {
    //         action(PrintShipmentPalletLabel)
    //         {
    //             ApplicationArea = All;
    //             Caption = 'Print Shipment Pallet Label';
    //             ToolTip = 'Print Shipment Pallet Label for selected warehouse shipment lines.';
    //             Image = Print;
    //             Promoted = true;
    //             PromotedCategory = Process;
    //             PromotedIsBig = true;
    //             PromotedOnly = true;

    //             trigger OnAction()
    //             var
    //                 WarehouseShipmentLineDtl: Record "Whse. Shipment Line Dtl. MXW";
    //             begin
    //                 CurrPage.SetSelectionFilter(WarehouseShipmentLineDtl);
    //                 if not WarehouseShipmentLineDtl.IsEmpty() then begin
    //                     WarehouseShipmentLineDtl.FindSet();
    //                     Message('Shipment label printing functionality will be implemented here.');
    //                 end;
    //             end;
    //         }
    //     }
}
//}
