{"name": "Maxwell Custom Ruleset", "description": "Custom ruleset for Maxwell Customizations project", "includedRuleSets": [{"action": "<PERSON><PERSON><PERSON>", "path": "https://raw.githubusercontent.com/StefanMaron/RulesetFiles/main/pte.rulset.json"}], "rules": [{"id": "LC0068", "action": "None", "justification": "Detects missing Permissions declarations for TableData access; ensures the Permissions property is set to avoid runtime permission errors and to support indirect permissions."}, {"id": "LC0084", "action": "None", "justification": "Encourages using function return values for proper error handling (use return value instead of ignoring results or relying on side-effects)."}, {"id": "LC0023", "action": "None", "justification": "Requires field groups 'DropDown' and 'Brick' to be defined on tables to ensure consistent UI grouping and layout."}, {"id": "LC0091", "action": "None", "justification": "Ensures translatable texts (Captions, ToolTips, Labels) are translated for required languages by checking project .xlf files; respects Locked labels and supports explicit languagesToTranslate configuration."}]}