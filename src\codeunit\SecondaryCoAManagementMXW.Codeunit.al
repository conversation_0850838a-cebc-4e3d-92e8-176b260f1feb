codeunit 60006 "Secondary CoA Management MXW"
{
    procedure TestNo2DataOnGLAccount(AnyLine: Variant)
    var
        GLAccount: Record "G/L Account";
        AccountNo: Code[20];
    begin
        if not TryResolveGLAccountFromLine(AnyLine, AccountNo) then
            exit;

        if not GLAccount.Get(AccountNo) then
            exit;

        GLAccount.TestField("No. 2");
        GLAccount.TestField("No. 2 Description MXW");
    end;

    procedure TryResolveGLAccountFromLine(AnyLine: Variant; var AccountNo: Code[20]): Boolean
    var
        RecRef: RecordRef;
    begin
        AccountNo := '';

        if not AnyLine.IsRecord() then
            exit(false);

        RecRef.GetTable(AnyLine);

        case RecRef.Number() of
            Database::"Gen. Journal Line":
                exit(TryGetGLAccountFromGenJnlLine(RecRef, AccountNo));
            Database::"Purchase Line":
                exit(TryGetGLAccountFromPurchLine(RecRef, AccountNo));
            Database::"Sales Line":
                exit(TryGetGLAccountFromSalesLine(RecRef, AccountNo));
        end;

        exit(false);
    end;

    procedure TryGetGLAccountFromGenJnlLine(var RecRef: RecordRef; var AccountNo: Code[20]): Boolean
    var
        GenJnlLine: Record "Gen. Journal Line";
    begin
        AccountNo := '';
        RecRef.SetTable(GenJnlLine);
        if GenJnlLine."Account Type" <> GenJnlLine."Account Type"::"G/L Account" then
            exit(false);
        AccountNo := GenJnlLine."Account No.";
        exit(AccountNo <> '');
    end;

    procedure TryGetGLAccountFromPurchLine(var RecRef: RecordRef; var AccountNo: Code[20]): Boolean
    var
        PurchLine: Record "Purchase Line";
    begin
        AccountNo := '';
        RecRef.SetTable(PurchLine);
        if PurchLine.Type <> PurchLine.Type::"G/L Account" then
            exit(false);
        AccountNo := PurchLine."No.";
        exit(AccountNo <> '');
    end;

    procedure TryGetGLAccountFromSalesLine(var RecRef: RecordRef; var AccountNo: Code[20]): Boolean
    var
        SalesLine: Record "Sales Line";
    begin
        AccountNo := '';
        RecRef.SetTable(SalesLine);
        if SalesLine.Type <> SalesLine.Type::"G/L Account" then
            exit(false);
        AccountNo := SalesLine."No.";
        exit(AccountNo <> '');
    end;
}
