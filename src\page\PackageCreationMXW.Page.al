page 60001 "Package Creation MXW"
{
    ApplicationArea = All;
    Caption = 'Package Creation';
    PageType = StandardDialog;
    SourceTable = "Package Creation MXW";
    SourceTableTemporary = true;

    layout
    {
        area(Content)
        {
            field("Source Type"; Rec."Source Type")
            {
                ToolTip = 'Specifies the source type.';
            }
            field("Document No."; Rec."Document No.")
            {
                Editable = DocumentNoEditable;
            }
            field("Document Line No."; Rec."Document Line No.")
            {
                Editable = DocumentLineNoEditable;
                ToolTip = 'Specifies the document line number.';
                Visible = false;
            }
            field("Item No."; Rec."Item No.")
            {
                Editable = false;
            }
            field("Variant Code"; Rec."Variant Code")
            {
                Editable = false;
            }
            field("Unit of Measure Code"; Rec."Unit of Measure Code")
            {
                Editable = false;
            }
            field("Item Description"; Rec."Item Description")
            {
                Editable = false;
                ToolTip = 'Specifies the item description.';
            }
            field("Lot No."; Rec."Lot No.")
            {
                Editable = false;
            }
            field("Expiration Date"; Rec."Expiration Date")
            {
                Editable = false;
            }
            field("Purchase Order No."; Rec."Purchase Order No.")
            {
                Editable = false;
                Visible = Rec."Source Type" = Rec."Source Type"::Purchase;
            }

            field("Max Available Quantity"; Rec."Max Available Quantity")
            {
                Caption = 'Max Available Quantity';
                Editable = false;
            }
            field("Creation Method"; Rec."Creation Method")
            {
                ToolTip = 'Specifies the creation method.';
                ValuesAllowed = Single, Multiple;
            }
            field("Order Quantity"; Rec."Order Quantity")
            {
                Editable = false;
            }
            field("Package Count"; Rec."Package Count")
            {
                Editable = Rec."Creation Method" = Rec."Creation Method"::Multiple;
                trigger OnValidate()
                var
                    TooMuchPackCountErr: Label 'You cannot enter more than 50 packages at once';
                begin
                    if Rec."Package Count" > 50 then
                        Error(TooMuchPackCountErr);

                    if DocumentIsPurchase then
                        CalcTotalPackageQuantity(Rec."Package Count", Rec."Package Quantity")
                    else
                        CalcSinglePackageQuantity(Rec."Package Count", TotalPackageQuantity);

                    if TotalPackageQuantity > Rec."Max Available Quantity" then
                        Error(CannotGreaterErr, TotalPackageQuantity, Rec."Max Available Quantity");
                end;
            }
            field("Package Quantity"; Rec."Package Quantity")
            {
                Editable = DocumentIsPurchase;
                trigger OnValidate()
                var
                    PalletItemMsg: Label 'The number of items per pallet is %1. You are trying to place %2 items on this pallet.', Comment = '%1="Package Creation MXW"."Item Pieces Of Pallet"; %2="Package Creation MXW"."Package Quantity"';
                begin
                    if DocumentIsPurchase then begin
                        CalcTotalPackageQuantity(Rec."Package Count", Rec."Package Quantity");

                        if (TotalPackageQuantity > Rec."Max Available Quantity") then
                            Error(CannotGreaterErr, TotalPackageQuantity, Rec."Max Available Quantity");

                        if Rec."Source Type" = Rec."Source Type"::Production then
                            if Rec."Package Quantity" > Rec."Item Pieces Of Pallet" then
                                Message(PalletItemMsg, Rec."Item Pieces Of Pallet", Rec."Package Quantity");
                    end
                end;
            }
            field("Total Package Quantity"; TotalPackageQuantity)
            {
                Caption = 'Total Package Quantity';
                ToolTip = 'Specifies the total package quantity.';
                Editable = not DocumentIsPurchase;
                trigger OnValidate()
                var
                    PalletItemMsg: Label 'The number of items per pallet is %1. You are trying to place %2 items on this pallet.', Comment = '%1="Package Creation MXW"."Item Pieces Of Pallet"; %2="Package Creation MXW"."Package Quantity"';
                begin
                    if not DocumentIsPurchase then begin
                        CalcSinglePackageQuantity(Rec."Package Count", TotalPackageQuantity);

                        if (TotalPackageQuantity > Rec."Max Available Quantity") then
                            Error(CannotGreaterErr, TotalPackageQuantity, Rec."Max Available Quantity");

                        if Rec."Source Type" = Rec."Source Type"::Production then
                            if Rec."Package Quantity" > Rec."Item Pieces Of Pallet" then
                                Message(PalletItemMsg, Rec."Item Pieces Of Pallet", Rec."Package Quantity");
                    end
                end;
            }
        }
    }

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
        NoPackageCreatedMsg: Label 'No package was created.';
    begin
        if not (CloseAction = Action::OK) then
            Message(NoPackageCreatedMsg)
        else
            case Rec."Source Type" of
                "Package Creation Src. Type MXW"::Purchase:
                    MaxwellPurchaseManagement.CreateWarehouseReceiptLineDetailsFromPackageCreation(Rec);
                "Package Creation Src. Type MXW"::Production:
                    MaxwellProductionManagement.CreateProductionOrderLineDetailsFromPackageCreation(Rec);
            end;
    end;

    trigger OnAfterGetRecord()
    var
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
    begin
        if Rec."Source Type" = Rec."Source Type"::Purchase then
            if WarehouseReceiptLine.Get(Rec."Document No.", Rec."Document Line No.") then begin
                WarehouseReceiptLine.CalcFields("Total Package Quantity MXW");
                DocumentIsPurchase := true;
                //Rec."Produced By" := Rec."Produced By"::" ";
            end;

        if Rec."Document No." = '' then
            DocumentNoEditable := true;

        CalcTotalPackageQuantity(Rec."Package Count", Rec."Package Quantity");
    end;

    local procedure CalcTotalPackageQuantity(PackageCount: Integer; PackageQuantity: Decimal)
    begin
        case Rec."Creation Method" of
            Rec."Creation Method"::Single:
                TotalPackageQuantity := PackageQuantity;
            Rec."Creation Method"::Multiple:
                TotalPackageQuantity := PackageCount * PackageQuantity;
        end;
    end;

    local procedure CalcSinglePackageQuantity(PackageCount: Integer; ParTotalPackageQuantity: Decimal)
    begin
        if (PackageCount > 0) and (ParTotalPackageQuantity > 0) then
            Rec."Package Quantity" := (ParTotalPackageQuantity / PackageCount);
    end;

    var
        MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
        MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
        TotalPackageQuantity: Decimal;
        DocumentIsPurchase: Boolean;
        DocumentNoEditable: Boolean;
        DocumentLineNoEditable: Boolean;
        CannotGreaterErr: Label 'Total package quantity %1 cannot be greater than max available quantity %2.', Comment = '%1=TotalPackageQuantity; %2=Max Available Quantity';
}
