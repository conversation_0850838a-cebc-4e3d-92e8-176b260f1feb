# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the **Maxwell Customizations** per-tenant extension (PTE) for Microsoft Dynamics 365 Business Central 26.0. It provides package-centric warehouse management capabilities for manufacturing and distribution companies, enabling tracking of individual packages throughout the supply chain from purchase receipt to sales shipment.

## Key Architecture Components

### Core Business Logic Structure
- **Business logic belongs in codeunits** - pages are thin UI surfaces
- **5 main management codeunits** handle distinct domains:
  - `MaxwellBasicFunctionsMXW` (60010): Core utilities for package/lot management and user operations
  - `MaxwellPurchaseMngtMXW` (60001): Package creation from warehouse receipts, QC document generation
  - `MaxwellProductionMngtMXW` (60002): Package creation from production orders, output journal management
  - `MaxwellSalesMngtMXW` (60004): Barcode scanning for warehouse shipments, package validation
  - `MaxwellPackageTransMgtMXW` (60007): Inter-location package transfers with traceability

### Package-Centric Data Model
- **Package Transfer Orders**: Custom document type for moving packages between locations
- **Package Creation**: Temporary table for UI dialogs with Single/Multiple creation methods
- **Detail tracking tables**: Link packages to warehouse receipts, shipments, and production orders
- **Quality Control integration**: Prevents package operations until QC documents are processed

### Extension Architecture
- **Object ID range**: 60000-60999 (strictly enforced)
- **Naming convention**: PascalCase with `MXW` suffix (e.g., `MaxwellSalesMngtMXW`)
- **Dependencies**: Quality Control Management (********) and G/L Account Names extensions
- **Features enabled**: `NoImplicitWith`, `TranslationFile`

## Development Commands

### Build and Package
```bash
# Primary build command - Use Ctrl+Shift+B in VS Code Insiders
powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('^+{b}')"

# Alternative via VS Code Command Palette
AL: Package

# Alternative via VS Code task
Ctrl+Shift+P → Tasks: Run Task → Build
```

### Debug and Deploy
```bash
# Debug against Maxwell-Sandbox environment
F5 (or use launch configuration "Maxwell-Sandbox")

# Manual deployment via PowerShell
.\VS Code Actions\InstallAppAsPTEToCustomer.ps1
```

### Important Build Notes
- **This project uses VS Code Insiders**, not standard VS Code
- **Always use the PowerShell SendKeys approach** to trigger Ctrl+Shift+B for building
- VS Code Insiders path: `C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\bin\code-insiders.cmd`

### Code Analysis
- **Enabled analyzers**: CodeCop, UICop, PerTenantExtensionCop, LinterCop
- **Custom ruleset**: `custom.ruleset.json` (based on PTE best practices with 4 rules disabled)
- **Background analysis**: Enabled for real-time feedback

## Microsoft AL Coding Guidelines

### Core Development Principles
- **Event-driven programming**: Never modify standard application objects - use integration events for extensibility
- **Business logic in codeunits**: Keep pages as thin UI surfaces
- **Small, focused procedures**: Write modular, single-responsibility functions
- **Consistent formatting**: Use 2-space indentation throughout

### Naming Conventions
- **Objects**: PascalCase, maximum 26 characters, descriptive names
  - Examples: `CustomerLedgerEntry`, `SalesInvoicePosting`, `PostSalesInvoice`
- **Files**: `<ObjectName>.<ObjectType>.al` pattern
  - Examples: `CustomerCard.Page.al`, `PostSalesInvoice.Codeunit.al`
- **Variables/Functions**: PascalCase, descriptive, avoid abbreviations
  - Good: `CustomerLedgerEntry`, `CalculateCustomerBalance()`
  - Bad: `CustLedgEntry`, `CalcCustBal()`
- **Interfaces**: Prefix with "I", implementations suffix with "Impl"
  - Examples: `ICustomerService`, `CustomerServiceImpl`
- **Parameters**: Descriptive and specific, avoid generic names like "Rec"

### Code Style Standards
- **Indentation**: Use 2 spaces consistently
- **Brackets**: Follow consistent bracket placement
- **Line length**: Keep lines readable, break long statements appropriately

### File Organization
- **Shared components**: Place in `Common` or `Shared` folders


### Variable Declaration Order
1. Record → Report → Codeunit → XmlPort → Page → Query → Notification
2. System types → Simple types → Complex types → Collections

### Performance Guidelines
- **Filter early**: Apply SetRange and SetFilter before processing records
- **Use SetLoadFields**: Load only necessary fields before Get/Find operations
- **Temporary storage**: Use temporary tables, dictionaries, and lists appropriately
- **Avoid loops**: Prefer set-based operations and built-in aggregation (CalcSums)
- **Batch operations**: Use for multiple record updates to minimize database hits

### Error Handling Best Practices
- **TryFunctions**: Use for graceful exception management
- **Error labels**: Always use label variables, never hardcoded text
  - Include comments for translators
  - Use Locked = true for technical messages
- **Context in errors**: Include variable values and specific record references


### Event-Driven Development
- **Integration events**: Create at logical business process points with meaningful names
- **Event parameters**: Pass records by reference, use descriptive parameter names
- **Handled patterns**: Implement to control execution flow
- **Event subscribers**: Use for extending standard application behavior

Example patterns:
```al
// Integration Event
[IntegrationEvent(false, false)]
procedure OnBeforeCreateCustomer(var Customer: Record Customer; var IsHandled: Boolean)
begin
end;

// Event Subscriber
[EventSubscriber(ObjectType::Table, Database::"Sales Header", OnBeforeInsert, '', false, false)]
local procedure OnBeforeInsertSalesHeader(var SalesHeader: Record "Sales Header")
begin
end;
```

### Project-Specific Rules
- **Object ID range**: 60000-60999 (strictly enforced)
- **Naming suffix**: Use `MXW` suffix (e.g., `MaxwellSalesMngtMXW`)
- **File placement**: `src/<objectType>/<ObjectName>.<ObjectType>.al`
- **Always use parentheses** for method calls (LC0077): `RecRef.Number()`
- **Explicit record operations**: `Insert(false)` instead of `Insert()`
- **Event subscribers**: Use identifier syntax, not quoted event names (LC0028)

### Translation Requirements
- **Keep translations synchronized** in `Translations/Maxwell Customizations.g.xlf`
- **Add captions/tooltips** to table fields (not page controls)
- **Target language**: Turkish (TRK) as configured in snippet settings

## Quality Control Integration Patterns

### QC Document Lifecycle
1. **Auto-creation**: Warehouse receipt lines automatically generate QC documents
2. **Status validation**: Package creation blocked until QC status = "Acceptance"
3. **Bulk processing**: "Accept All Quality Control Documents" for efficient processing
4. **Error handling**: Specific error messages guide users to process QC first

### Implementation Pattern
```al
// Check QC status before package operations
if QualityControlDocStat = QualityControlDocStat::" " then
    Error('You cannot create package without processing...');
```

## Package Management Patterns

### Package Creation Architecture
- **Single packages**: One package with full quantity
- **Multiple packages**: Distribute quantity across multiple packages with validation
- **Automatic workflows**: Label printing, item tracking assignment, output journal creation

### Barcode Integration
- **Code128 format**: Primary barcode standard for package numbers
- **QR codes**: Enhanced 2D barcodes for production labels
- **Scanner integration**: Real-time validation with user confirmation for expired items

### Inter-Location Transfers
- **Package Transfer Orders**: Custom document type with barcode scanning
- **Item reclassification**: Backend journal processing for inventory movements
- **Validation rules**: Location matching, quantity constraints, duplicate prevention

## Common Development Tasks

### Adding New Package Operations
1. **Create codeunit function** in appropriate management codeunit
2. **Add page action** to trigger the operation
3. **Implement validation logic** using `MaxwellBasicFunctionsMXW` utilities
4. **Add error handling** with specific user guidance
5. **Update permission set** if new objects are created

### Extending Quality Control
1. **Reference QCM extension enums** for status consistency
2. **Use existing QC document patterns** for creation and linking
3. **Implement status validation** before allowing operations
4. **Add bulk processing capabilities** for efficiency

### Label and Report Customization
- **RDLC layouts**: Located in `src/reportlayout/` with multiple format variations
- **Barcode integration**: Uses ID Automation font provider
- **Print triggers**: Automatic after package creation or manual via page actions

## Error Handling Patterns

Follow the Success and Error Message Guidelines at `C:\Users\<USER>\Dropbox\AL\_CustomPrompts\SuccessAndErrorMessageGuidelines.md`:
- **Use specific record references** with identifiers
- **Past tense for completion** messages
- **Include relevant details** like record counts or numbers
- **Guide users to resolution** with actionable instructions

## Testing and Validation

- **No automated test framework** configured - manual testing required
- **Sandbox environment**: "Maxwell-Sandbox" for testing and debugging
- **Quality gates**: Address CodeCop/UICop/PTECop findings per custom ruleset
- **Data validation**: Package uniqueness, quantity consistency, location tracking
