# Lot Expiration Checking Implementation

## Overview
Implemented lot expiration checking functionality for Maxwell Customizations PTE to warn users when processing expired inventory in Consumption Journal and Warehouse Shipment barcode reading scenarios.

## Implementation Details

### 1. Core Functions in `Maxwell Basic Functions MXW` (MaxwellBasicFunctionsMXW.Codeunit.al)

#### Added Functions:
- `IsLotExpired(ItemNo, VariantCode, LotNo): Boolean` - Checks if a lot is expired based on Lot No. Information table
- `IsPackageExpired(ItemNo, VariantCode, PackageNo): Boolean` - Checks if a package is expired based on Package No. Information table  
- `ConfirmExpiredLotUsage(ItemNo, VariantCode, LotNo): Boolean` - Shows confirmation dialog for expired lots
- `ConfirmExpiredPackageUsage(ItemNo, VariantCode, PackageNo): Boolean` - Shows confirmation dialog for expired packages

#### Logic:
- Checks expiration date against Today() function
- If expiration date is 0D (not set), considers item not expired
- Shows user-friendly confirmation dialog with lot/package info and expiration date
- Throws error with "Process aborted due to expired inventory selection" if user chooses No

### 2. Warehouse Shipment Barcode Processing Enhancement

#### Modified: `Maxwell Whse. Shipment Mgt MXW` (MaxwellWhseShipmentMgtMXW.Codeunit.al)
- Added expiration check in `ProcessBarcodeForWarehouseShipment` procedure
- Calls `ConfirmExpiredPackageUsage` after finding package but before other validations
- Uses existing package information already retrieved for barcode processing

### 3. Consumption Journal Lot Selection Enhancement

#### Modified: `Maxwell Events MXW` (MaxwellEventsMXW.Codeunit.al)
- Added event subscriber for `Tracking Specification.OnAfterValidateEvent."Lot No."` 
- Added event subscriber for `Item Journal Line.OnAfterValidateEvent."Lot No."`
- Validates expiration only when:
  - Lot No. field value actually changes
  - Lot No. is not empty
  - For Item Journal Line: Entry Type is Consumption

## User Experience
1. **Consumption Journal**: When user selects or enters a lot number that is expired, shows confirmation dialog
2. **Warehouse Shipment**: When user scans barcode for an expired package, shows confirmation dialog
3. **Confirmation Dialog**: "Lot/Package No. X for Item Y has expired on Z. Do you want to continue processing with expired inventory?"
4. **Process Aborted**: If user selects No, shows error "Process aborted due to expired inventory selection."

## Technical Notes
- Uses existing `Expiration Date MXW` fields in both Lot No. Information and Package No. Information tables
- Follows project coding standards and patterns
- Integrates with existing barcode and lot tracking workflows
- No new codeunit needed - uses existing Maxwell Basic Functions for utility functions
- Package No. Information.Get() requires 3 parameters: ItemNo, VariantCode, PackageNo

## Testing Recommendations
1. Test with lots that have expiration dates in the past
2. Test with lots that have no expiration date set (should not trigger warning)
3. Test user selecting "Yes" to continue with expired inventory
4. Test user selecting "No" to abort process
5. Test both consumption journal lot selection and warehouse shipment barcode scanning scenarios