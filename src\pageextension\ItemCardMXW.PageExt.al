pageextension 60000 "Item Card MXW" extends "Item Card"
{
    layout
    {
        addlast(content)
        {
            group("Maxwell MXW")
            {
                Caption = 'Maxwell';
                field("Packages Per Pallet MXW"; Rec."Packages Per Pallet MXW")
                {
                    ApplicationArea = All;
                }
                field("Unit per Parcel MXW"; Rec."Units per Parcel")
                {
                    ApplicationArea = All;
                    Caption = 'Unit per Parcel';
                    ToolTip = 'Specifies the number of units per parcel for this item.';
                }
            }
        }
    }

    actions
    {
        addlast(processing)
        {
            group("Maxwell Functions MXW")
            {
                Caption = 'Maxwell Functions';
                Image = LotInfo;

                action("Create Assembly BOM MXW")
                {
                    ApplicationArea = All;
                    Caption = 'Create Assembly BOM from Production BOM';
                    ToolTip = 'Creates an Assembly BOM from the Production BOM assigned to this item.';
                    Image = BOM;
                    Promoted = true;
                    PromotedCategory = Process;
                    PromotedIsBig = true;

                    trigger OnAction()
                    var
                        MaxwellBOMMngtMXW: Codeunit "Maxwell BOM Management MXW";
                    begin
                        MaxwellBOMMngtMXW.ConvertProductionBOMToAssemblyBOM(Rec."No.");
                    end;
                }
            }
        }
    }
}
