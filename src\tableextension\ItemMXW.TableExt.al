tableextension 60004 "Item MXW" extends Item
{
    fields
    {
        field(60000; "Packages Per Pallet MXW"; Integer)
        {
            Caption = 'Packages Per Pallet';
            ToolTip = 'Specifies the number of packages per pallet for this item.';
        }
        field(60001; "Expiration Date Formula MXW"; DateFormula)
        {
            Caption = 'Expiration Date Formula';
            ToolTip = 'Specifies the formula to calculate expiration date from receipt date (e.g., +30D for 30 days).';
        }
        field(60002; "Default Shelf Life Days MXW"; Integer)
        {
            Caption = 'Default Shelf Life Days';
            ToolTip = 'Specifies the default shelf life in days. Used when Expiration Date Formula is not specified.';
            MinValue = 0;
        }
    }
}
