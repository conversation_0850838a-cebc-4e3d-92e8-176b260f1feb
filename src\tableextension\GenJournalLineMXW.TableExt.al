tableextension 60028 "Gen. Journal Line MXW" extends "Gen. Journal Line"
{
    fields
    {
        field(60000; "No. 2 MXW"; Code[20])
        {
            Caption = 'No. 2';
            ToolTip = 'Specifies the secondary number of the G/L Account when Account Type is G/L Account.';
            FieldClass = FlowField;
            CalcFormula = lookup("G/L Account"."No. 2" where("No." = field("Account No.")));
            Editable = false;
        }
        field(60001; "No. 2 Description MXW"; Text[100])
        {
            Caption = 'No. 2 Description';
            ToolTip = 'Specifies the secondary number description of the G/L Account when Account Type is G/L Account.';
            FieldClass = FlowField;
            CalcFormula = lookup("G/L Account"."No. 2 Description MXW" where("No." = field("Account No.")));
            Editable = false;
        }
    }
}
