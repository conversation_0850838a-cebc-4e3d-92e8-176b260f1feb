# Utility Commands for Windows
- `git` for version control
- `ls` or `dir` for listing files
- `cd` for changing directories
- `findstr` for searching text in files
- `code .` to open VS Code in the current directory
- `powershell` for scripting and automation
- `uvx` and `uv` for Serena and Python environment management
- `AL: Download Symbols` and `AL: Package` for AL development
- Use Command Palette (Ctrl+Shift+P) for all AL and MCP/Serena commands in VS Code