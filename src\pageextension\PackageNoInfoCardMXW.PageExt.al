pageextension 60063 "Package No. Info Card MXW" extends "Package No. Information Card"
{
    layout
    {
        addafter(General)
        {
            group("Maxwell MXW")
            {
                Caption = 'Maxwell';
                field("Lot No. MXW"; Rec."Lot No. MXW")
                {
                    ApplicationArea = All;
                }
                field("Document No. MXW"; Rec."Document No. MXW")
                {
                    ApplicationArea = All;
                }
                field("Expiration Date MXW"; Rec."Expiration Date MXW")
                {
                    ApplicationArea = All;
                }
                field("Label Quantity MXW"; Rec."Label Quantity MXW")
                {
                    ApplicationArea = All;
                }
                field("Pallet Sequence No. MXW"; Rec."Pallet Sequence No. MXW")
                {
                    ApplicationArea = All;
                }
                field("Location Code MXW"; Rec."Location Code MXW")
                {
                    ApplicationArea = All;
                }
                field("Inventory MXW"; Rec.Inventory)
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the quantity on inventory with this line.';
                }
            }
        }
    }

    actions
    {
        addafter("&Package")
        {
            action("Print Label MXW")
            {
                AccessByPermission = tabledata "Serial No. Information" = I;
                ApplicationArea = ItemTracking;
                Caption = 'Print Label';
                Image = Print;
                ToolTip = 'Prints the palette label for this package.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                    PaletteLabel: Report "Palette Label MXW";
                begin
                    PackageNoInformation := Rec;
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    PaletteLabel.SetTableView(PackageNoInformation);
                    PaletteLabel.RunModal();
                end;
            }
        }
    }
}
