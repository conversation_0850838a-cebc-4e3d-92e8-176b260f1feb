tableextension 60027 "G/L Entry MXW" extends "G/L Entry"
{
    fields
    {
        field(60000; "G/L Account No. 2 MXW"; Code[20])
        {
            Caption = 'G/L Account No. 2';
            ToolTip = 'Specifies the No. 2 from the G/L Account.';
            FieldClass = FlowField;
            CalcFormula = lookup("G/L Account"."No. 2" where("No." = field("G/L Account No.")));
            Editable = false;
        }
    }
}
