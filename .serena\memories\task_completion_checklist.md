# What to do when a task is completed
- Run `AL: Package` to build and verify the extension
- Ensure all analyzers (CodeCop, PerTenantExtensionCop, UICop) pass with no errors
- Test the change in the Maxwell-Sandbox environment
- If relevant, update documentation (README.md, SETUP_COMPLETE.md)
- Commit changes to git with a meaningful message
- If new objects or fields are added, update translations in Translations/
- If new business logic is added, ensure it follows the code style and conventions
- If Serena is used, update memories as needed for future context