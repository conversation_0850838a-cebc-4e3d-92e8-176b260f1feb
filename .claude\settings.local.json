{"permissions": {"allow": ["<PERSON><PERSON>(code:*)", "Bash(alc.exe:*)", "Bash(where alc)", "<PERSON><PERSON>(dir:*)", "Bash(where code)", "Bash(\"C:\\Program Files\\Microsoft VS Code\\bin\\code.cmd\" . --wait --command workbench.action.tasks.runTask --args \"Build\")", "Bash(where code-insiders)", "Bash(\"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin\\code-insiders.cmd\" . --wait --command workbench.action.tasks.runTask --args \"Build\")", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(powershell:*)", "mcp__microsoft_docs_mcp__microsoft_docs_search", "WebSearch", "WebFetch(domain:www.atlassian.com)", "WebFetch(domain:github.com)", "WebFetch(domain:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude restart)", "mcp__atlassian__getAccessibleAtlassianResources", "mcp__atlassian__getVisibleJiraProjects", "mcp__atlassian__searchJiraIssuesUsingJql", "mcp__atlassian__getJiraIssue", "mcp__microsoft_docs_mcp__microsoft_docs_fetch", "mcp__atlassian__lookupJiraAccountId", "mcp__atlassian__getJiraProjectIssueTypesMetadata", "mcp__atlassian__createJiraIssue", "mcp__atlassian__getTransitionsForJiraIssue", "mcp__atlassian__transitionJiraIssue", "mcp__atlassian__editJiraIssue", "WebFetch(domain:demiliani.com)", "WebFetch(domain:raw.githubusercontent.com)", "WebFetch(domain:api.github.com)"], "deny": [], "ask": []}}