# Code Style and Conventions
- Object Naming: <PERSON><PERSON><PERSON>, max 30 chars, descriptive, use namespace pattern `<PERSON>XW<PERSON><PERSON>.FeatureName`
- Variable Order: Record, Report, Codeunit, XmlPort, Page, Query, Notification, then system types, then simple types, then complex types, then collections
- Procedure Calls: Always use brackets, even with no parameters
- Event Subscribers: Use signatures without quotes around event names
- Record Operations: Always use explicit parameters (e.g., Insert(false))
- JSON: Use AL's built-in JSON types and direct Get methods (e.g., GetText())
- TempBlob: Use streams directly from CreateInStream()/CreateOutStream()
- Multiline Text: Use AL's @'...' syntax
- Conditionals: Use else if as separate else and if blocks; prefer case true of for multiple conditions
- Tooltips: Apply at table field level
- ApplicationArea: Set at object level, except for field-level in PageExtension/TableExtension
- Follow folder structure and documentation patterns as in existing codeunits/pages