page 60011 "Prod. Order Line Details MXW"
{
    PageType = List;
    SourceTable = "Prod. Order Line Detail MXW";
    ApplicationArea = All;
    Caption = 'Prod. Order Line Detail';
    UsageCategory = Lists;
    ModifyAllowed = false;
    InsertAllowed = false;


    layout
    {
        area(Content)
        {
            repeater(Group)
            {
                field(Status; Rec.Status)
                {
                }
                field("Prod. Order No."; Rec."Prod. Order No.")
                {
                }
                field("Prod. Order Line No."; Rec."Prod. Order Line No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        if Rec."Package No." = '' then
                            exit;
                        PackageNoInformation.SetRange("Package No.", Rec."Package No.");
                        if PackageNoInformation.FindFirst() then
                            Page.Run(Page::"Package No. Information Card", PackageNoInformation);
                    end;
                }
                field("Item No."; Rec."Item No.")
                {
                    trigger OnDrillDown()
                    var
                        Item: Record Item;
                    begin
                        if Rec."Item No." = '' then
                            exit;
                        Item.Get(Rec."Item No.");
                        Page.Run(Page::"Item Card", Item);
                    end;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                    trigger OnDrillDown()
                    var
                        LotNoInformation: Record "Lot No. Information";
                    begin
                        if Rec."Lot No." = '' then
                            exit;
                        LotNoInformation.SetRange("Lot No.", Rec."Lot No.");
                        if LotNoInformation.FindFirst() then
                            Page.Run(Page::"Lot No. Information Card", LotNoInformation);
                    end;
                }
                field("Expiration Date"; Rec."Expiration Date")
                {
                }
                field("Pallet Sequence No. MXW"; Rec."Pallet Sequence No. MXW")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field(Posted; Rec.Posted)
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(CreateOutputJournal)
            {
                Caption = 'Create Output Journals';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Create Output Journals action.';
                PromotedOnly = true;
                Enabled = not Rec.Posted;

                trigger OnAction()
                var
                    ProdOrderLineDetail: Record "Prod. Order Line Detail MXW";
                    MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
                begin
                    CurrPage.SetSelectionFilter(ProdOrderLineDetail);
                    ProdOrderLineDetail.FindSet();
                    repeat
                        MaxwellProductionManagement.CreateOutputJournalsFromProdOrderLineDetail(ProdOrderLineDetail);
                    //MaxwellProductionManagement.CreateConsumptionJournalsFromProdOrderLineDetail(ProdOrderLineDetail);
                    //MaxwellProductionManagement.PostConsumptionAndOutputJournals(ProdOrderLineDetail);
                    until ProdOrderLineDetail.Next() = 0;
                end;
            }
            action(PrintPaletteLabel)
            {
                Caption = 'Print Palette Label';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Print;
                ToolTip = 'Print palette labels for the selected package numbers.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    ProdOrderLineDetail: Record "Prod. Order Line Detail MXW";
                    PackageNoInformation: Record "Package No. Information";
                    PaletteLabelReport: Report "Palette Label MXW";
                    PackageNoFilter: Text;
                begin
                    CurrPage.SetSelectionFilter(ProdOrderLineDetail);
                    if not ProdOrderLineDetail.FindSet() then
                        exit;

                    // Build filter for Package No. Information based on selected lines
                    repeat
                        if ProdOrderLineDetail."Package No." <> '' then begin
                            if PackageNoFilter <> '' then
                                PackageNoFilter += '|';
                            PackageNoFilter += ProdOrderLineDetail."Package No.";
                        end;
                    until ProdOrderLineDetail.Next() = 0;

                    if PackageNoFilter = '' then begin
                        Message('No package numbers found in selected lines.');
                        exit;
                    end;

                    // Filter Package No. Information records and run report
                    PackageNoInformation.SetFilter("Package No.", PackageNoFilter);
                    if PackageNoInformation.FindFirst() then begin
                        PaletteLabelReport.SetTableView(PackageNoInformation);
                        PaletteLabelReport.Run();
                    end else
                        Message('No package information found for selected package numbers.');
                end;
            }
            // action(CreateConsJournal)
            // {
            //     Caption = 'Create Consumption Journals';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     Image = PostOrder;
            //     ToolTip = 'Executes the Create Consumption Journals action.';
            //     PromotedOnly = true;

            //     trigger OnAction()
            //     var
            //         ProdOrderLineDetail: Record "Prod. Order Line Detail MXW";
            //         MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
            //     begin
            //         CurrPage.SetSelectionFilter(ProdOrderLineDetail);
            //         ProdOrderLineDetail.FindSet();
            //         repeat
            //             MaxwellProductionManagement.CreateConsumptionJournalsFromProdOrderLineDetail(ProdOrderLineDetail);
            //         until ProdOrderLineDetail.Next() = 0;
            //     end;
            // }
        }
    }
}
