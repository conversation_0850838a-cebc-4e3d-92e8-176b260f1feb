codeunit 60001 "Maxwell Purchase Mngt. MXW"
{
    SingleInstance = true;

    procedure CreatePackageFromWarehouseReceiptLine(WarehouseReceiptLine: Record "Warehouse Receipt Line"; PackageCreationMethod: Enum "Package Creation Method MXW")
    var
        Item: Record Item;
        TempPackageCreation: Record "Package Creation MXW" temporary;
        QualityDocumentErr: Label 'You cannot create package without processing the corresponding line''s quality control document';
    begin
        Item.Get(WarehouseReceiptLine."Item No.");
        WarehouseReceiptLine.CalcFields("Total Package Quantity MXW");

        //Quality control check using QCM enums
        if (WarehouseReceiptLine."Quality Control Doc. Stat. MXW" = "Quality Control Status QCM"::" ") or
           (WarehouseReceiptLine."Quality Control Doc. Stat. MXW" = "Quality Control Status QCM"::"Input Pending") then
            Error(QualityDocumentErr);

        // Validate expiration date if mandatory based on Item Tracking Code
        ValidateExpirationDateMandatory(Item, WarehouseReceiptLine);

        TempPackageCreation.Init();
        TempPackageCreation."Source Type" := TempPackageCreation."Source Type"::Purchase;
        TempPackageCreation."Document No." := WarehouseReceiptLine."No.";
        TempPackageCreation."Document Line No." := WarehouseReceiptLine."Line No.";
        // LotNoInformation description will be populated when creating Lot No. Information
        TempPackageCreation.Validate("Item No.", WarehouseReceiptLine."Item No.");
        TempPackageCreation.Validate("Variant Code", WarehouseReceiptLine."Variant Code");
        TempPackageCreation."Lot No." := WarehouseReceiptLine."Lot No. MXW";
        TempPackageCreation."Creation Method" := PackageCreationMethod;
        TempPackageCreation."Purchase Order No." := WarehouseReceiptLine."Source No.";
        TempPackageCreation."Unit of Measure Code" := WarehouseReceiptLine."Unit of Measure Code";

        // Populate expiration date from warehouse receipt line
        if WarehouseReceiptLine."Expiration Date MXW" <> 0D then
            TempPackageCreation."Expiration Date" := WarehouseReceiptLine."Expiration Date MXW";

        // Get item pieces per pallet from item card
        if Item."Packages Per Pallet MXW" <> 0 then
            TempPackageCreation.Validate("Item Pieces Of Pallet", Item."Packages Per Pallet MXW");

        TempPackageCreation.Validate("Order Quantity", WarehouseReceiptLine.Quantity);
        TempPackageCreation.Validate("Max Available Quantity", WarehouseReceiptLine.Quantity - WarehouseReceiptLine."Total Package Quantity MXW");

        if PackageCreationMethod = PackageCreationMethod::Single then
            TempPackageCreation."Package Count" := 1;

        TempPackageCreation.Insert(false);

        Page.Run(Page::"Package Creation MXW", TempPackageCreation);
    end;

    local procedure ValidateExpirationDateMandatory(Item: Record Item; WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        ItemTrackingCode: Record "Item Tracking Code";
        ExpirationDateMandatoryErr: Label 'Expiration Date is mandatory for item %1 based on its Item Tracking Code settings.', Comment = '%1=Item No.';
    begin
        if Item."Item Tracking Code" = '' then
            exit;

        if not ItemTrackingCode.Get(Item."Item Tracking Code") then
            exit;

        if ItemTrackingCode."Man. Expir. Date Entry Reqd." and (WarehouseReceiptLine."Expiration Date MXW" = 0D) then
            Error(ExpirationDateMandatoryErr, WarehouseReceiptLine."Item No.");
    end;

    procedure CreateWarehouseReceiptLineDetailsFromPackageCreation(var TempPackageCreation: Record "Package Creation MXW" temporary)
    var
        i: Integer;
    begin
        TempPackageCreation.TestField("Package Count");
        TempPackageCreation.TestField("Package Quantity");

        case TempPackageCreation."Creation Method" of
            TempPackageCreation."Creation Method"::Single:
                InsertWarehouseReceiptLineDetail(TempPackageCreation);
            TempPackageCreation."Creation Method"::Multiple:
                for i := 1 to TempPackageCreation."Package Count" do
                    InsertWarehouseReceiptLineDetail(TempPackageCreation);
        end;

        GlobalWarehouseReceiptLineDtl.MarkedOnly(true);
        Commit();//
        Report.Run(Report::"Purchase Pallet Label MXW", true, true, GlobalWarehouseReceiptLineDtl);
        Clear(GlobalWarehouseReceiptLineDtl);
    end;

    local procedure InsertWarehouseReceiptLineDetail(var TempPackageCreation: Record "Package Creation MXW" temporary)
    var
        MaxwellSetup: Record "Maxwell Setup MXW";
        InventorySetup: Record "Inventory Setup";
        WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW";
        NoSeries: Codeunit "No. Series";
    begin
        MaxwellSetup.GetRecordOnce();
        InventorySetup.GetRecordOnce();
        InventorySetup.TestField("Package Nos.");

        WarehouseReceiptLineDtl.Init();
        WarehouseReceiptLineDtl."Document No." := TempPackageCreation."Document No.";
        WarehouseReceiptLineDtl."Document Line No." := TempPackageCreation."Document Line No.";
        WarehouseReceiptLineDtl.Validate("Item No.", TempPackageCreation."Item No.");
        WarehouseReceiptLineDtl.Validate("Variant Code", TempPackageCreation."Variant Code");
        WarehouseReceiptLineDtl.Validate("Lot No.", TempPackageCreation."Lot No.");
        WarehouseReceiptLineDtl.Validate(Quantity, TempPackageCreation."Package Quantity");
        WarehouseReceiptLineDtl.Validate("Package No.", NoSeries.GetNextNo(InventorySetup."Package Nos."));
        WarehouseReceiptLineDtl."Purchase Order No." := TempPackageCreation."Purchase Order No.";
        WarehouseReceiptLineDtl."Expiration Date" := TempPackageCreation."Expiration Date";
        WarehouseReceiptLineDtl."Unit of Measure Code" := TempPackageCreation."Unit of Measure Code";
        WarehouseReceiptLineDtl."Package Count" := TempPackageCreation."Package Count";
        WarehouseReceiptLineDtl."Total Package Qty." := TempPackageCreation."Package Quantity";
        WarehouseReceiptLineDtl.Insert(true);

        GlobalWarehouseReceiptLineDtl := WarehouseReceiptLineDtl;
        GlobalWarehouseReceiptLineDtl.Mark(true);

        CreateLotNoInformation(WarehouseReceiptLineDtl);
    end;

    // procedure PrintPaletteLabels()
    // begin
    //     GlobalPackageNoInformation.MarkedOnly(true);
    //     Commit();//
    //     Report.Run(Report::"Purchase Pallet Label MXW", true, true, GlobalPackageNoInformation);
    //     Clear(GlobalPackageNoInformation);
    // end;

    // local procedure CreatePackageNoInformation(WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW")
    // var
    //     PackageNoInformation: Record "Package No. Information";
    // begin
    //     if not PackageNoInformation.Get(WarehouseReceiptLineDtl."Item No.", WarehouseReceiptLineDtl."Variant Code", WarehouseReceiptLineDtl."Package No.") then begin
    //         PackageNoInformation.Init();
    //         PackageNoInformation."Item No." := WarehouseReceiptLineDtl."Item No.";
    //         PackageNoInformation."Variant Code" := WarehouseReceiptLineDtl."Variant Code";
    //         PackageNoInformation."Package No." := WarehouseReceiptLineDtl."Package No.";
    //         PackageNoInformation."Lot No. MXW" := WarehouseReceiptLineDtl."Lot No.";
    //         PackageNoInformation."Document No. MXW" := WarehouseReceiptLineDtl."Document No.";
    //         PackageNoInformation.Insert(true);
    //     end;

    //     // Add to global collection for printing
    //     GlobalPackageNoInformation.Copy(PackageNoInformation);
    //     GlobalPackageNoInformation.Mark(true);
    // end;

    local procedure CreateLotNoInformation(WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW")
    var
        LotNoInformation: Record "Lot No. Information";
        Item: Record Item;
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
    begin
        if not LotNoInformation.Get(WarehouseReceiptLineDtl."Item No.", WarehouseReceiptLineDtl."Variant Code", WarehouseReceiptLineDtl."Lot No.") then begin
            LotNoInformation.Init();
            LotNoInformation."Item No." := WarehouseReceiptLineDtl."Item No.";
            LotNoInformation."Variant Code" := WarehouseReceiptLineDtl."Variant Code";
            LotNoInformation."Lot No." := WarehouseReceiptLineDtl."Lot No.";

            // Populate Description with Item Description
            if Item.Get(WarehouseReceiptLineDtl."Item No.") then
                // Use helper to obtain description (item + optional variant)
                LotNoInformation.Description := BasicFuncs.GetItemDescription(WarehouseReceiptLineDtl."Item No.", WarehouseReceiptLineDtl."Variant Code");

            // Set extension fields
            LotNoInformation."Purchase Order No. MXW" := WarehouseReceiptLineDtl."Purchase Order No.";

            // Set Expiration Date if provided
            if WarehouseReceiptLineDtl."Expiration Date" <> 0D then
                LotNoInformation."Expiration Date MXW" := WarehouseReceiptLineDtl."Expiration Date";

            LotNoInformation.Insert(true);
        end;
    end;

    procedure AssignItemTrackingInformationFromWarehouseReceiptLine(WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW";
        //ItemTrackAssignedMsg: Label 'Item Tracking Information has been assigned.';
        TotalAssignedQuantity: Decimal;
    begin
        WarehouseReceiptLineDtl.SetRange("Document No.", WarehouseReceiptLine."No.");
        WarehouseReceiptLineDtl.SetRange("Document Line No.", WarehouseReceiptLine."Line No.");
        WarehouseReceiptLineDtl.SetRange("Item Tracking Info Assignd MXW", false);
        if WarehouseReceiptLineDtl.FindSet(true) then
            repeat
                AssignItemTrackingInformationFromWarehouseReceiptLineDetail(WarehouseReceiptLineDtl);
                WarehouseReceiptLineDtl."Item Tracking Info Assignd MXW" := true;
                WarehouseReceiptLineDtl.Modify(true);
                TotalAssignedQuantity += WarehouseReceiptLineDtl.Quantity;
            until WarehouseReceiptLineDtl.Next() = 0;

        WarehouseReceiptLine."Item Tracking Info Assignd MXW" := true;

        // Populate Quantity to Receive with total quantity from just-assigned package records
        WarehouseReceiptLine.Validate("Qty. to Receive", TotalAssignedQuantity);
        WarehouseReceiptLine.Modify(true);
        //Message(ItemTrackAssignedMsg);
    end;

    procedure AssignItemTrackingInformationFromWarehouseReceiptHeader(WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
        ItemTrackAssignedMsg: Label 'Item Tracking Information has been assigned.';
    begin
        WarehouseReceiptLine.SetRange("No.", WarehouseReceiptHeader."No.");
        WarehouseReceiptLine.SetRange("Item Tracking Info Assignd MXW", false);
        if WarehouseReceiptLine.FindSet(true) then
            repeat
                AssignItemTrackingInformationFromWarehouseReceiptLine(WarehouseReceiptLine);
            // Note: AssignItemTrackingInformationFromWarehouseReceiptLine already sets
            // "Item Tracking Info Assignd MXW" := true and modifies the record,
            // so we don't need to do it again here to avoid overwriting Qty. to Receive
            until WarehouseReceiptLine.Next() = 0;
        Message(ItemTrackAssignedMsg);
    end;

    local procedure AssignItemTrackingInformationFromPurchaseLine(LotNo: Code[50]; PackageNo: Code[50]; var PurchaseLine: Record "Purchase Line"; AvailabilityDate: Date; QtyToReceiveBase: Decimal; QtyToReceive: Decimal; ExpirationDate: Date)
    var
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        PurchLineReserve: Codeunit "Purch. Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
    begin
        PurchLineReserve.InitFromPurchLine(TempSourceTrackingSpecification, PurchaseLine);

        TempTrackingSpecification.Init();
        TempTrackingSpecification."Lot No." := LotNo;
        if PackageNo <> '' then
            TempTrackingSpecification."Package No." := PackageNo;
        if ExpirationDate <> 0D then
            TempTrackingSpecification."Expiration Date" := ExpirationDate;

        TempTrackingSpecification.SetQuantities(QtyToReceiveBase,
                                                QtyToReceive,
                                                QtyToReceiveBase,
                                                0,
                                                0,
                                                0,
                                                0);
        TempTrackingSpecification.Insert(false);

        //ItemTrackingLines.SetBlockCommit(true);
        OverrideQuantityHandled := true;
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, AvailabilityDate, TempTrackingSpecification);
        OverrideQuantityHandled := false;
    end;

    procedure GenerateQualityControlLineFromWarehouseReceiptLine(var WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        QualityControlHeaderQCM: Record "Quality Control Header QCM";
        QualityControlMngtSetupQCM: Record "Quality Control Mngt Setup QCM";
        ExistingReceiptLine: Record "Warehouse Receipt Line";
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        QualityControlManagementQCM: Codeunit "Quality Control Management QCM";
        NoSeries: Codeunit "No. Series";
    //QualityControlCreatedMsg: Label 'Quality Control Document %1 has been created.', Comment = '%1=Quality Control Document No.';
    begin
        // Check for existing line with same Item No. and Lot No. MXW in the same document
        ExistingReceiptLine.Reset();
        ExistingReceiptLine.SetRange("No.", WarehouseReceiptLine."No.");
        ExistingReceiptLine.SetRange("Item No.", WarehouseReceiptLine."Item No.");
        ExistingReceiptLine.SetRange("Lot No. MXW", WarehouseReceiptLine."Lot No. MXW");
        // Exclude the current line itself
        ExistingReceiptLine.SetFilter("Line No.", '<>%1', WarehouseReceiptLine."Line No.");
        if ExistingReceiptLine.FindFirst() then
            if ExistingReceiptLine."Quality Control Doc. No. MXW" <> '' then begin
                WarehouseReceiptLine.Validate("Quality Control Doc. No. MXW", ExistingReceiptLine."Quality Control Doc. No. MXW");
                exit;
            end;

        //MaxwellSetup.GetRecordOnce();
        QualityControlMngtSetupQCM.GetRecordOnce();
        QualityControlMngtSetupQCM.TestField("Quality Control No. Series");

        QualityControlHeaderQCM.Init();
        QualityControlHeaderQCM."No." := NoSeries.GetNextNo(QualityControlMngtSetupQCM."Quality Control No. Series");
        QualityControlHeaderQCM.Type := "Quality Control Type QCM"::Purchase;
        QualityControlHeaderQCM.Validate("Item No.", WarehouseReceiptLine."Item No.");
        QualityControlHeaderQCM.Validate("Variant Code", WarehouseReceiptLine."Variant Code");
        QualityControlHeaderQCM."Lot No." := WarehouseReceiptLine."Lot No. MXW";
        QualityControlHeaderQCM.Date := Today();
        QualityControlHeaderQCM.Status := "Quality Control Status QCM"::"Input Pending";
        QualityControlHeaderQCM."Source Document No." := WarehouseReceiptLine."No.";
        QualityControlHeaderQCM."Source Document Line No." := WarehouseReceiptLine."Line No.";
        QualityControlHeaderQCM."Quantity" := WarehouseReceiptLine.Quantity;
        // Try to populate Vendor No. from related Purchase document (if any)
        if PurchaseLine.Get(WarehouseReceiptLine."Source Subtype", WarehouseReceiptLine."Source No.", WarehouseReceiptLine."Source Line No.") then
            if PurchaseHeader.Get(PurchaseLine."Document Type", PurchaseLine."Document No.") then
                if PurchaseHeader."Buy-from Vendor No." <> '' then
                    QualityControlHeaderQCM.Validate("Vendor No.", PurchaseHeader."Buy-from Vendor No.");

        QualityControlHeaderQCM.Insert(true);

        WarehouseReceiptLine.Validate("Quality Control Doc. No. MXW", QualityControlHeaderQCM."No.");
        // WarehouseReceiptLine.Modify(true);

        QualityControlManagementQCM.PopulateQualityControlLines(QualityControlHeaderQCM);

        //Message(QualityControlCreatedMsg, QualityControlHeaderQCM."No.");
    end;

    procedure AssignItemTrackingInformationFromWarehouseReceiptLineDetail(WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW")
    var
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
    begin
        WarehouseReceiptLine.Get(WarehouseReceiptLineDtl."Document No.", WarehouseReceiptLineDtl."Document Line No.");
        PurchaseLine.Get(WarehouseReceiptLine."Source Subtype", WarehouseReceiptLine."Source No.", WarehouseReceiptLine."Source Line No.");
        PurchaseHeader.Get(PurchaseLine."Document Type", PurchaseLine."Document No.");

        AssignItemTrackingInformationFromPurchaseLine(WarehouseReceiptLineDtl."Lot No.", '', PurchaseLine, PurchaseHeader."Posting Date", WarehouseReceiptLineDtl.Quantity, WarehouseReceiptLineDtl.Quantity, WarehouseReceiptLineDtl."Expiration Date");
    end;

    // procedure WarehouseReceipt_VendorShipmentNo_OnAfterValidate(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    // var
    //     QualityControlHeaderQCM: Record "Quality Control Header QCM";
    // begin
    //     QualityControlHeaderQCM.SetRange("Source Document No.", WarehouseReceiptHeader."No.");
    //     //QualityControlHeaderQCM.ModifyAll("External Document No.", WarehouseReceiptHeader."Vendor Shipment No.", true);
    // end;

    procedure AssignLotNoToItemJournalLine(var ItemJournalLine: Record "Item Journal Line"; LotNo: Code[50]; QtyBase: Decimal; Qty: Decimal; PackageNo: Code[50]; ExpirationDate: Date)
    var
        LotNoInformation: Record "Lot No. Information";
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        ItemJnlLineReserve: Codeunit "Item Jnl. Line-Reserve";
        BasicFuncs: Codeunit "Maxwell Basic Functions MXW";
        ItemTrackingLines: Page "Item Tracking Lines";
    begin
        // Use the standard Business Central approach for item tracking
        // Populate LotNoInformation description using the Item/Variant from the item journal line
        ItemJnlLineReserve.InitFromItemJnlLine(TempSourceTrackingSpecification, ItemJournalLine);
        LotNoInformation.Description := BasicFuncs.GetItemDescription(ItemJournalLine."Item No.", ItemJournalLine."Variant Code");
        TempTrackingSpecification.Init();
        TempTrackingSpecification."Lot No." := LotNo;
        if PackageNo <> '' then
            TempTrackingSpecification."Package No." := PackageNo;
        if ExpirationDate <> 0D then
            TempTrackingSpecification."Expiration Date" := ExpirationDate;

        // For transfer operations, set the "New Lot No." to the same value
        // This follows the standard BC pattern for transfers where the lot number stays the same
        if ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Transfer then begin
            TempTrackingSpecification."New Lot No." := LotNo;
            TempTrackingSpecification."New Package No." := PackageNo;
            if ExpirationDate <> 0D then
                TempTrackingSpecification."New Expiration Date" := ExpirationDate;
        end;

        TempTrackingSpecification.SetQuantities(QtyBase,
                                                Qty,
                                                QtyBase,
                                                0,
                                                0,
                                                0,
                                                0);
        TempTrackingSpecification.Insert(false);

        //ItemTrackingLines.SetBlockCommit(true);
        if ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Transfer then
            ItemTrackingLines.SetRunMode(Enum::"Item Tracking Run Mode"::Reclass);

        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, ItemJournalLine."Posting Date", TempTrackingSpecification);
    end;

    procedure HandleItemTrackingLinesOnAfterCopyTrackingSpec(var SourceTrackingSpec: Record "Tracking Specification"; var DestTrkgSpec: Record "Tracking Specification")
    begin
        if OverrideQuantityHandled then
            DestTrkgSpec."Quantity Handled (Base)" := SourceTrackingSpec."Quantity Handled (Base)";
    end;

    procedure ProcessWarehouseReceiptHeaderFromRequest(var WhseReceiptHeader: Record "Warehouse Receipt Header")
    var
        PurchaseHeader: Record "Purchase Header";
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
    begin
        WarehouseReceiptLine.SetRange("No.", WhseReceiptHeader."No.");
        WarehouseReceiptLine.DeleteQtyToReceive(WarehouseReceiptLine);
        WarehouseReceiptLine.Reset();
        WarehouseReceiptLine.SetRange("No.", WhseReceiptHeader."No.");
        if WarehouseReceiptLine.FindFirst() then begin
            PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, WarehouseReceiptLine."Source No.");
            WhseReceiptHeader.Validate("Vendor No. MXW", PurchaseHeader."Pay-to Vendor No.");
            WhseReceiptHeader.Modify(true);
        end;
    end;

    procedure ProcessWarehouseReceiptHeaderFromSingleDoc(var WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        PurchaseHeader: Record "Purchase Header";
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
    begin
        WarehouseReceiptLine.Reset();
        WarehouseReceiptLine.SetRange("No.", WarehouseReceiptHeader."No.");
        if WarehouseReceiptLine.FindFirst() then begin
            PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, WarehouseReceiptLine."Source No.");
            WarehouseReceiptHeader.Validate("Vendor No. MXW", PurchaseHeader."Pay-to Vendor No.");
            WarehouseReceiptHeader.Modify(true);
        end;
    end;

    procedure ProcessWarehouseReceiptLineOnInsert(var WarehouseReceiptLine: Record "Warehouse Receipt Line")
    var
        ExistingReceiptLine: Record "Warehouse Receipt Line";
        Item: Record Item;
        NoSeries: Codeunit "No. Series";
    begin
        if not Item.Get(WarehouseReceiptLine."Item No.") then
            exit;

        // Check for existing line with same Item No. in the same document
        ExistingReceiptLine.Reset();
        ExistingReceiptLine.SetRange("No.", WarehouseReceiptLine."No.");
        ExistingReceiptLine.SetRange("Item No.", WarehouseReceiptLine."Item No.");
        // Exclude the current line itself (in case of re-insert)
        ExistingReceiptLine.SetFilter("Line No.", '<>%1', WarehouseReceiptLine."Line No.");
        if ExistingReceiptLine.FindFirst() then
            WarehouseReceiptLine."Lot No. MXW" := ExistingReceiptLine."Lot No. MXW"
        else
            WarehouseReceiptLine."Lot No. MXW" := NoSeries.GetNextNo(Item."Lot Nos.");

        GenerateQualityControlLineFromWarehouseReceiptLine(WarehouseReceiptLine);
    end;

    procedure HandleOnAfterInsertItemLedgerEntry(var Rec: Record "Item Ledger Entry"; RunTrigger: Boolean)
    var
        WarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW";
    begin
        // Only process if the trigger is running and we have package information
        // if not RunTrigger then
        //     exit;

        if (Rec."Lot No." = '') then
            exit;

        // Update the "Received" field for matching warehouse receipt line details
        //WarehouseReceiptLineDtl.SetRange("Package No.", Rec."Package No.");
        WarehouseReceiptLineDtl.SetRange("Item No.", Rec."Item No.");
        WarehouseReceiptLineDtl.SetRange("Lot No.", Rec."Lot No.");

        if not WarehouseReceiptLineDtl.IsEmpty() then
            WarehouseReceiptLineDtl.ModifyAll(Received, true, true);
    end;

    procedure AcceptAllQualityControlDocuments(WarehouseReceiptHeader: Record "Warehouse Receipt Header")
    var
        WarehouseReceiptLine: Record "Warehouse Receipt Line";
        QualityControlHeaderQCM: Record "Quality Control Header QCM";
        AcceptedCount: Integer;
        NoQCDocumentsMsg: Label 'No Quality Control documents found for this warehouse receipt.';
        QCDocumentsAcceptedMsg: Label '%1 Quality Control document(s) have been accepted.', Comment = '%1=Number of accepted documents';
    begin
        AcceptedCount := 0;

        // Find all warehouse receipt lines for this document
        WarehouseReceiptLine.SetRange("No.", WarehouseReceiptHeader."No.");
        WarehouseReceiptLine.SetFilter("Quality Control Doc. No. MXW", '<>%1', '');

        if WarehouseReceiptLine.FindSet() then
            repeat
                // Get the Quality Control document and accept it if it's not already processed
                if QualityControlHeaderQCM.Get(WarehouseReceiptLine."Quality Control Doc. No. MXW") then
                    if (QualityControlHeaderQCM.Status = "Quality Control Status QCM"::" ") or
                       (QualityControlHeaderQCM.Status = "Quality Control Status QCM"::"Input Pending") then begin
                        QualityControlHeaderQCM.Status := "Quality Control Status QCM"::Acceptance;
                        QualityControlHeaderQCM.Modify(true);
                        AcceptedCount += 1;
                    end;
            until WarehouseReceiptLine.Next() = 0;

        if AcceptedCount = 0 then
            Message(NoQCDocumentsMsg)
        else
            Message(QCDocumentsAcceptedMsg, AcceptedCount);
    end;

    var
        GlobalWarehouseReceiptLineDtl: Record "Warehouse Receipt Line Dtl MXW";
        OverrideQuantityHandled: Boolean;


}
