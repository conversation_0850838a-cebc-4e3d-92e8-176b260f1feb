# Maxwell Customizations

## Overview
Custom modifications and enhancements for Maxwell's Business Central environment.
# Maxwell Customizations (Per-Tenant Extension)

## Overview

This repository contains the Maxwell Customizations per-tenant extension (PTE) for Microsoft Dynamics 365 Business Central. The extensions in this repo target Business Central 26.0 and contain AL objects that extend purchasing, production, sales, warehouse, and packaging workflows for Maxwell Food.

## Quick facts

- App target: Business Central 26.0 (see `app.json`).
- Object ID range: 60000–60999 — choose IDs inside this range for new objects.
- Important features: `NoImplicitWith` and `TranslationFile` are enabled.

## Repository layout

- `app.json` — package metadata (version, dependencies, runtime/app targets).
- `src/` — AL source grouped by object type (`codeunit/`, `page/`, `pageextension/`, `table/`, `tableextension/`, `report/`, `reportlayout/`, `enum/`, `permissionset/`).
- `Translations/` — XLF translation files. Generated `*.g.xlf` appears here during build.
- `custom.ruleset.json` — project-specific analyzer rules.

## Development workflow

1. Prerequisites
  - VS Code with the AL extension installed.
  - Download Business Central symbols (Run: "AL: Download Symbols").

2. Build / package
  - Use the VS Code command: "AL: Package" or run the workspace "Build" task.

3. Debug / publish
  - Ensure `launch.json` targets the correct sandbox (for example: "Maxwell-Sandbox"). Use the AL extension to publish and debug.

4. Translations
  - Keep translations in `Translations/Maxwell Customizations.g.xlf` synchronized with captions/tooltips you add.

## Conventions & coding guidelines

- Object names: PascalCase with `MXW` suffix when appropriate (e.g., `MaxwellSalesMngtMXW`).
- Variable declaration order: Record → Report → Codeunit → XmlPort → Page → Query → Notification → system types → simple types → complex types → collections.
- Always use parentheses for property/method access (LC0077). Example: `RecRef.Number()`.
- Use explicit record ops (e.g., `Insert(false)`).
- Event subscribers: use identifier syntax (no quoted event names) (LC0028).
- Keep business logic in codeunits; pages should be thin UI surfaces.

## Testing and quality gates

- Run the Build task to generate the `.app` package and surface AL errors.
- Address CodeCop, PerTenantExtensionCop, and UICop findings according to `custom.ruleset.json` and the project's standards.

## Adding new objects

- Choose an ID within 60000–60999 and follow naming conventions.
- Place files under the appropriate `src/<objectType>/` folder with the standard filename format: `<ObjectName>.<ObjectType>.al` (for example `src/codeunit/MaxwellPurchaseMngtMXW.Codeunit.al`).
- Add captions/tooltips to table fields (put tooltips on table fields rather than page controls) and update XLF translations.

## Useful references in the repo

- `src/codeunit/MaxwellPurchaseMngtMXW.Codeunit.al` — example purchase flows and number series handling.
- `src/page/MaxwellSetupMXW.Page.al` and `src/table/MaxwellSetupMXW.Table.al` — setup/config patterns.

## Contact

For questions or help, contact the Maxwell development team or open an issue in this repository with relevant details (object names, steps to reproduce, error messages).

## Change log

- 2025-08-15: README rewritten and consolidated; targets updated to Business Central 26.0; developer guidelines expanded.

---

Files changed: `README.md` — refreshed with consolidated guidance and updated target information.
---
