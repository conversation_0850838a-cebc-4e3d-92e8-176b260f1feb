pageextension 60015 "Released Prod. Order Lines MXW" extends "Released Prod. Order Lines"
{
    layout
    {
        addafter("Remaining Quantity")
        {
            field("Lot No. MXW"; Rec."Lot No. MXW")
            {
                ApplicationArea = All;
                ShowMandatory = true;
            }
            field("Expiration Date MXW"; Rec."Expiration Date MXW")
            {
                ApplicationArea = All;
                ShowMandatory = true;
            }
        }
    }
    actions
    {
        addlast("&Line")
        {
            action("CreatePackage MXW")
            {
                ApplicationArea = All;
                Caption = 'Create Package';
                Image = Action;
                ToolTip = 'Create a package for this production order line.';
                trigger OnAction()
                var
                    MaxwellProductionManagement: Codeunit "Maxwell Production Mngt.MXW";
                begin
                    MaxwellProductionManagement.PreparePackageCreationFromProdOrderLine(Rec);
                end;
            }
            action("Packages MXW")
            {
                ApplicationArea = All;
                Image = ShipmentLines;
                Caption = 'Packages';
                RunObject = page "Prod. Order Line Details MXW";
                RunPageLink = Status = field(Status), "Prod. Order No." = field("Prod. Order No."), "Prod. Order Line No." = field("Line No.");
                ToolTip = 'Show package details for this production order line.';
            }
        }
    }
}
