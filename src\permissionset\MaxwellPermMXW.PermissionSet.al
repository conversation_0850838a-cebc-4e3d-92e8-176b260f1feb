permissionset 60000 "Maxwell Perm MXW"
{
    Caption = 'Maxwell Permission MXW', MaxLength = 30;
    Assignable = true;
    Permissions = tabledata "Maxwell Setup MXW" = RIMD,
        tabledata "Package Creation MXW" = RIMD,
        tabledata "Package Transfer Header MXW" = RIMD,
        tabledata "Package Transfer Line MXW" = RIMD,
        tabledata "Warehouse Receipt Line Dtl MXW" = RIMD,
        tabledata "Whse. Shipment Line Dtl. MXW" = RIMD,
        table "Maxwell Setup MXW" = X,
        table "Package Creation MXW" = X,
        table "Package Transfer Header MXW" = X,
        table "Package Transfer Line MXW" = X,
        table "Warehouse Receipt Line Dtl MXW" = X,
        table "Whse. Shipment Line Dtl. MXW" = X,
        codeunit "Maxwell Events MXW" = X,
        codeunit "Maxwell Purchase Mngt. MXW" = X,
        codeunit "Maxwell Package Trans. Mgt MXW" = X,
        codeunit "Maxwell BOM Management MXW" = X,
        page "Maxwell Setup MXW" = X,
        page "Package Creation MXW" = X,
        page "Package Transfer Order MXW" = X,
        page "Package Transfer Orders MXW" = X,
        page "Package Transfer Subpage MXW" = X,
        page "Package Transfer Lines MXW" = X,
        page "Package Transfer Details MXW" = X,
        page "Whse. Receipt Line Details MXW" = X,
        page "Whse. Shipment Line Dtl MXW" = X,
        report "Palette Label MXW" = X,
        tabledata "Prod. Order Line Detail MXW" = RIMD,
        table "Prod. Order Line Detail MXW" = X,
        page "Prod. Order Line Details MXW" = X,
        codeunit "Maxwell Production Mngt.MXW" = X,
        report "Purchase Pallet Label MXW" = X,
        codeunit "Maxwell Basic Functions MXW" = X,
        codeunit "Maxwell Sales Mngt. MXW" = X,
        codeunit "Secondary CoA Management MXW" = X;
}
