{"CRS.ReorganizeByNamespace": false, "al.rootNamespace": "MXW", "alOutline.additionalMandatoryAffixesPatterns": [" MXW"], "alOutline.defaultDataClassification": "Customer<PERSON><PERSON>nt", "alOutline.enableCodeCopFixes": true, "alOutline.fixCaseRemovesQuotesFromDataTypeIdentifiers": true, "alOutline.fixCodeCopMissingParenthesesOnSave": true, "alOutline.noEmptyLinesAtTheEndOfWizardGeneratedFiles": true, "alOutline.openDefinitionInNewTab": true, "linterCop.load-pre-releases": true, "alVarHelper.ignoreALSuffix": "MXW", "alNavigator.ignoreALSuffix": "MXW", "CRS.OnSaveAlFileAction": "Reorganize", "CRS.ObjectNameSuffix": " MXW", "al.enableCodeAnalysis": true, "al.enableCodeCop": true, "al.enablePerTenantExtensionCop": true, "al.enableUICop": true, "al.enableExternalRulesets": true, "al.codeAnalyzers": ["${CodeCop}", "${UICop}", "${PerTenantExtensionCop}", "${analyzerFolder}BusinessCentral.LinterCop.dll"], "al.ruleSetPath": "custom.ruleset.json", "al.incrementalBuild": true, "al.backgroundCodeAnalysis": true, "al.packageCachePath": ".alpackages", "al.enablePackageIntelliSense": true, "editor.codeLens": false, "editor.minimap.enabled": false, "editor.snippetSuggestions": "bottom", "ALTB.snippetTargetLanguage": "TRK", "xliffSync.snippetTargetLanguage": "TRK", "alOutline.activeBuildConfiguration": "", "github.copilot.chat.defaultInstructions": "Always follow the Success and Error Message Guidelines located at C:\\Users\\<USER>\\Dropbox\\AL\\_CustomPrompts\\SuccessAndErrorMessageGuidelines.md when creating user-facing messages. Use specific record references, past tense for completion, and include relevant details like record counts or identifiers.", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": true}