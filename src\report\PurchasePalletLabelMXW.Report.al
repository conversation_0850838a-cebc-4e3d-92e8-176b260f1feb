report 60000 "Purchase Pallet Label MXW"
{
    ApplicationArea = All;
    Caption = 'Purchase Pallet Label';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(WarehouseReceiptLineDtlMXW; "Warehouse Receipt Line Dtl MXW")
        {
            column(PackageNo_WarehouseReceiptLineDtlMXW; "Package No.")
            {
            }
            column(DocumentLineNo_WarehouseReceiptLineDtlMXW; "Document Line No.")
            {
            }
            column(ItemNo; "Item No.")
            {
            }
            column(ItemDescription; "Item Description")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(LotNo; "Lot No.")
            {
            }
            column(UnitofMeasureCode; "Unit of Measure Code")
            {
            }
            column(PurchaseOrderNo; "Purchase Order No.")
            {
            }
            column(ExpirationDate_WarehouseReceiptLineDtlMXW; "Expiration Date")
            {
            }
            column(PackageCount_WarehouseReceiptLineDtlMXW; "Package Count")
            {
            }
            column(TotalPackageQty_WarehouseReceiptLineDtlMXW; "Total Package Qty.")
            {
            }
            column(CreatedByFullName_WarehouseReceiptLineDtlMXW; CreatedByFullName)
            {
            }
            column(ModifiedByFullName_WarehouseReceiptLineDtlMXW; ModifiedByFullName)
            {
            }
            column(CheckedBy_WarehouseReceiptLineDtlMXW; "Checked By MXW")
            {
            }

            trigger OnAfterGetRecord()
            begin
                CalcFields("Checked By MXW");
                CreatedByFullName := BasicFunc.GetUserFullNameFromSecurityId(SystemCreatedBy);
                ModifiedByFullName := BasicFunc.GetUserFullNameFromSecurityId(SystemModifiedBy);
            end;
        }
    }

    var
        BasicFunc: Codeunit "Maxwell Basic Functions MXW";
        CreatedByFullName: Text[80];
        ModifiedByFullName: Text[80];
}
