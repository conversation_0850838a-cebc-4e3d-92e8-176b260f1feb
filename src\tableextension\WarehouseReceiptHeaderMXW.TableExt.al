tableextension 60001 "Warehouse Receipt Header MXW" extends "Warehouse Receipt Header"
{
    fields
    {
        field(60000; "Vendor No. MXW"; Code[20])
        {
            Caption = 'Vendor No.';
            ToolTip = 'Specifies the vendor number.';
            TableRelation = Vendor."No.";
            trigger OnValidate()
            var
                Vendor: Record Vendor;
            begin
                CalcFields("Vendor Name MXW");
                if Vendor.Get(Rec."Vendor No. MXW") then
                    Rec."Location Code" := Vendor."Location Code";
            end;
        }
        field(60001; "Vendor Name MXW"; Text[100])
        {
            Caption = 'Vendor Name';
            ToolTip = 'Specifies the vendor name.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Vendor.Name where("No." = field("Vendor No. MXW")));
        }
        field(60002; "Quality Control Not Proc. MXW"; Boolean)
        {
            FieldClass = FlowField;
            Caption = 'Quality Control Not Processed';
            ToolTip = 'Specifies whether quality control has not been processed.';
            AllowInCustomizations = Always;
            Editable = false;
            CalcFormula = exist("Warehouse Receipt Line" where("No." = field("No."), "Quality Control Doc. Stat. MXW" = filter("Input Pending" | " ")));
        }
        field(60003; "Checked By MXW"; Code[50])
        {
            Caption = 'Checked By';
            ToolTip = 'Specifies who checked the warehouse receipt.';
            DataClassification = ToBeClassified;
            TableRelation = "User Setup"."User ID";
        }
        // modify("Vendor Shipment No.")
        // {
        //     // trigger OnAfterValidate()
        //     // begin
        //     //     MaxwellPurchaseManagement.WarehouseReceipt_VendorShipmentNo_OnAfterValidate(Rec);
        //     // end;
        // }
    }

    // var
    // MaxwellPurchaseManagement: Codeunit "Maxwell Purchase Mngt. MXW";
}
