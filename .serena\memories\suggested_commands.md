# Suggested Commands
- Download symbols: `AL: Download Symbols` (VS Code Command Palette)
- Build/package: `AL: Package` (VS Code Command Palette)
- Run/test: Use Business Central sandbox (Maxwell-Sandbox)
- Linting/analysis: Analyzers run automatically (CodeCop, PerTenantExtensionCop, UICop)
- Git: Use standard git commands (Windows: git, ls, cd, findstr, etc.)
- Authentication: Ensure connection to Maxwell-Sandbox, tenant ID preconfigured
- For Serena: Use MCP: List Servers and MCP: Start Server in VS Code to enable Serena tools
- For troubleshooting: Check .vscode/settings.json and .vscode/mcp.json for config
- For translation: Use XLF files in Translations/
- For custom rules: See custom.ruleset.json
- For documentation: See README.md and SETUP_COMPLETE.md