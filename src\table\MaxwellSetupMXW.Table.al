table 60000 "Maxwell Setup MXW"
{
    Caption = 'Maxwell Setup';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        // field(2; "Package Nos."; Code[20])
        // {
        //     Caption = 'Package Nos.';
        //     TableRelation = "No. Series".Code;
        //     ToolTip = 'Specifies the number series for package numbers.';
        // }
        // field(3; "Default Warehouse Location"; Code[10])
        // {
        //     Caption = 'Default Warehouse Location';
        //     TableRelation = Location.Code;
        //     ToolTip = 'Specifies the default warehouse location.';
        // }
        // field(4; "Quality Control Required"; Boolean)
        // {
        //     Caption = 'Quality Control Required';
        //     ToolTip = 'Specifies whether quality control is required for warehouse receipts.';
        // }
        // field(5; "Auto Assign Item Tracking"; Boolean)
        // {
        //     Caption = 'Auto Assign Item Tracking';
        //     ToolTip = 'Specifies whether to automatically assign item tracking information.';
        // }
        // field(6; "Quality Control Nos."; Code[20])
        // {
        //     Caption = 'Quality Control Nos.';
        //     TableRelation = "No. Series".Code;
        //     ToolTip = 'Specifies the number series for quality control documents.';
        // }
        field(7; "Package Transfer Nos. MXW"; Code[20])
        {
            Caption = 'Package Transfer Nos.';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the number series for package transfer documents.';
        }
        field(8; "Package Tran. Jnl. Temp. MXW"; Code[10])
        {
            Caption = 'Package Transfer Journal Template Name';
            ToolTip = 'Specifies the journal template name for package transfers.';
            TableRelation = "Item Journal Template".Name where(Type = const(Transfer));
        }
        field(9; "Package Tran. Jnl. Batch MXW"; Code[10])
        {
            Caption = 'Package Transfer Journal Batch Name';
            ToolTip = 'Specifies the journal batch name for package transfers.';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Package Tran. Jnl. Temp. MXW"));
        }
        field(10; "Consumption Jnl. Template MXW"; Code[10])
        {
            Caption = 'Consumption Journal Template Name';
            TableRelation = "Item Journal Template" where(Type = const(Consumption));
            ToolTip = 'Specifies the journal template name for consumption journals.';
        }
        field(11; "Consumption Jnl. Batch MXW"; Code[10])
        {
            Caption = 'Consumption Journal Batch Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Consumption Jnl. Template MXW"));
            ToolTip = 'Specifies the journal batch name for consumption journals.';
        }
        field(12; "Output Journal Template MXW"; Code[10])
        {
            Caption = 'Output Journal Template Name';
            TableRelation = "Item Journal Template" where(Type = const(Output));
            ToolTip = 'Specifies the journal template name for output journals.';
        }
        field(13; "Output Journal Batch MXW"; Code[10])
        {
            Caption = 'Output Journal Batch Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Output Journal Template MXW"));
            ToolTip = 'Specifies the journal batch name for output journals.';
        }

        field(14; "Default Output Location Code"; Code[10])
        {
            Caption = 'Default Output Location Code';
            TableRelation = Location.Code;
            ToolTip = 'Specifies the default output location.';
        }
        field(15; "Expiration Warning Days MXW"; Integer)
        {
            Caption = 'Expiration Warning Days';
            ToolTip = 'Specifies how many days before expiration to send warning notifications.';
            MinValue = 1;
        }
        field(16; "Notification Frequency MXW"; Enum "Notification Frequency MXW")
        {
            Caption = 'Notification Frequency';
            ToolTip = 'Specifies how often to send expiration warning notifications.';
        }
        field(17; "Exp Notif Email MXW"; Text[250])
        {
            Caption = 'Expiration Notification Email';
            ToolTip = 'Specifies the email address to send expiration notifications to. Use semicolon to separate multiple addresses.';
            ExtendedDatatype = EMail;
        }
        field(18; "Last Notification Date MXW"; Date)
        {
            Caption = 'Last Notification Date';
            ToolTip = 'Specifies the date when the last expiration notification was sent.';
            Editable = false;
        }
    }

    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }

    var
        RecordHasBeenRead: Boolean;

    procedure GetRecordOnce()
    begin
        if RecordHasBeenRead then
            exit;
        Get();
        RecordHasBeenRead := true;
    end;

    procedure InsertIfNotExists()
    begin
        Reset();
        if not Get() then begin
            Init();
            Insert(true);
        end;
    end;
}
